# SyncMasterPro - Quick Start Guide

## 🚀 Quick Installation

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Setup Application

```bash
npm run setup
```

### Step 3: Start Application

```bash
npm start
```

## 🖥️ Platform-Specific Instructions

### Windows Users

- Double-click the installer or run `npm start`

### Mac/Linux Users

```bash
chmod +x start.sh
./start.sh
```

### Manual Start (Development)

```bash
# Terminal 1: Backend
npm run server

# Terminal 2: Frontend
npm run dev

# Terminal 3: Electron
npm run electron-dev
```

## 📱 First Time Setup

### 1. Create Account

- Open the app
- Click "Create Account" or use offline mode
- Set up your profile

### 2. Create Your First Sync Task

- Click "New Sync Task"
- Select source and destination folders
- Choose sync type (bidirectional recommended)
- Configure filters if needed
- Save the task

### 3. Start Syncing

- Click "Start" on your sync task
- Monitor progress in the dashboard
- Check sync history for details

## ⚙️ Basic Configuration

### Environment Setup

```env
# Basic settings
NODE_ENV=development
PORT=5000
DB_TYPE=sqlite

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
```

### Common Settings

- **Real-time sync**: Files sync immediately when changed
- **Scheduled sync**: Set specific times for synchronization
- **Filters**: Exclude certain file types or folders
- **Conflict resolution**: Choose how to handle file conflicts

## 🔧 Quick Troubleshooting

### App Won't Start?

1. Check Node.js version: `node --version` (need 16+)
2. Reinstall dependencies: `npm install`
3. Clear cache: `npm run clean`

### Sync Not Working?

1. Check folder permissions
2. Verify paths exist
3. Review sync task settings
4. Check logs for errors

### Performance Issues?

1. Exclude large files/folders
2. Use incremental sync
3. Adjust sync frequency
4. Enable compression

## 🎯 What You Can Do

### ✅ What You Can Do

- **Real-time sync** between any folders
- **Scheduled synchronization** at specific times
- **File filtering** by type, size, or pattern
- **Conflict resolution** with multiple strategies
- **Sync history** and detailed logging
- **Web management** interface
- **Multiple sync tasks** running simultaneously

### 🎛️ Sync Types

- **Bidirectional**: Changes sync both ways
- **Source to Destination**: One-way sync
- **Mirror**: Exact copy (deletes extra files)
- **Incremental**: Only sync changed files

### 🔍 Filters & Exclusions

- File types: `*.tmp`, `*.log`
- Folders: `node_modules`, `.git`
- Size limits: Skip files over X MB
- Date filters: Only recent files

## 🌐 Web Interface

### Features

- Remote sync management
- Multi-client monitoring
- Centralized configuration
- Real-time status updates

### Dashboard

- Active sync tasks
- System status
- Performance metrics
- Recent activity

### History

- Complete sync logs
- Error tracking
- Performance analytics
- Export capabilities

### Notifications

- Desktop notifications
- Email alerts (coming soon)
- Webhook integration
- Custom triggers

## 🔒 Security Features

### Local Security

- Encrypted local database
- Secure file handling
- Permission management
- Access control

### Network Security

- HTTPS support
- JWT authentication
- Session management
- API rate limiting

## 🛠️ Advanced Features

### Automation

```bash
# Schedule with cron
0 */6 * * * /path/to/syncmasterpro/sync-task.sh

# Windows Task Scheduler
schtasks /create /tn "SyncMasterPro" /tr "npm start" /sc daily
```

### Command Line

```bash
# CLI operations
npm run sync-task --id=1
npm run export-data
npm run backup-config
```

### API Access

```javascript
// Programmatic access
const api = require('./api/client');
await api.createSyncTask(config);
await api.startSync(taskId);
```

### Plugins (Coming Soon)

- Cloud storage integration
- Custom sync algorithms
- Third-party notifications
- Advanced filtering

## 📚 Getting Help

### Documentation

- Full documentation: `README.md`
- API docs: `/docs/api`
- Configuration guide: `INSTALL.md`

### Support

- GitHub Issues: Report bugs and feature requests
- Email: <<EMAIL>>

### Logs

- **Windows**: `%APPDATA%/SyncMasterPro/logs/`
- **macOS**: `~/Library/Logs/SyncMasterPro/`
- **Linux**: `~/.local/share/SyncMasterPro/logs/`

## 🎯 Next Steps

### Immediate

1. Set up your important sync tasks
2. Configure filters for your needs
3. Test different sync types
4. Explore the web interface

### Advanced

1. Explore web interface
2. Set up automation
3. Configure notifications
4. Try API integration

### Coming Soon

- Mobile app for monitoring
- Cloud storage integration
- Advanced conflict resolution
- Team collaboration features

---

🎉 **You're ready to sync!** Start with a simple folder sync and explore more features as you go.
