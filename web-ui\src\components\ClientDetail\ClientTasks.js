import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';

const ClientTasks = ({ client }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const { token } = useAuth();

  const loadClientTasks = useCallback(async () => {
    if (!token || !client?.client_id) return;

    try {
      setLoading(true);
      setError(null);

      console.log(`🔄 Loading tasks for client: ${client.client_id}`);

      const response = await fetch(`http://localhost:5001/api/sync/tasks?client_id=${client.client_id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load tasks: ${response.status}`);
      }

      const data = await response.json();
      const clientTasks = data.tasks || [];

      console.log(`✅ Loaded ${clientTasks.length} tasks for client ${client.client_id}`);
      setTasks(clientTasks);

    } catch (error) {
      console.error('❌ Failed to load client tasks:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [token, client?.client_id]);

  useEffect(() => {
    if (client?.client_id) {
      loadClientTasks();
    }
  }, [client?.client_id, token, loadClientTasks]);

  const sendTaskCommand = async (taskId, command) => {
    try {
      console.log(`🔧 Sending ${command} command for task ${taskId}`);

      const response = await fetch(`http://localhost:5001/api/clients/${client.client_id}/command`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: command,
          data: { taskId }
        })
      });

      if (response.ok) {
        console.log(`✅ ${command} command sent successfully`);
        // Refresh tasks after command
        setTimeout(loadClientTasks, 1000);
      } else {
        console.error(`❌ Failed to send ${command} command: ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ Error sending ${command} command:`, error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running': return '🟢';
      case 'paused': return '🟡';
      case 'stopped': return '⚪';
      case 'error': return '🔴';
      default: return '⚪';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading tasks...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading tasks</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Sync Tasks ({tasks.length})
        </h3>
        <button
          onClick={loadClientTasks}
          className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
        >
          Refresh
        </button>
      </div>

      {/* Tasks List */}
      {tasks.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <div className="text-gray-400 text-4xl mb-2">📋</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No sync tasks</h3>
          <p className="text-gray-500">This client doesn't have any sync tasks configured yet.</p>
        </div>
      ) : (
        <div className="space-y-3">
          {tasks.map((task) => (
            <div key={task.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <span className="text-lg mr-2">{getStatusIcon(task.status)}</span>
                  <h4 className="font-medium text-gray-900">{task.name}</h4>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                  {task.status}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                <div>
                  <span className="font-medium">Source:</span>
                  <div className="text-gray-900 font-mono text-xs mt-1 truncate" title={task.source_path}>
                    {task.source_path}
                  </div>
                </div>
                <div>
                  <span className="font-medium">Destination:</span>
                  <div className="text-gray-900 font-mono text-xs mt-1 truncate" title={task.destination_path}>
                    {task.destination_path}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  <span>Type: {task.sync_type}</span>
                  {task.last_sync && (
                    <span className="ml-4">Last sync: {new Date(task.last_sync).toLocaleString()}</span>
                  )}
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedTask(task)}
                    className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                  >
                    Details
                  </button>
                  
                  {task.status === 'running' ? (
                    <button
                      onClick={() => sendTaskCommand(task.id, 'pause-sync')}
                      className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs hover:bg-yellow-200"
                      disabled={client.status !== 'online'}
                    >
                      Pause
                    </button>
                  ) : (
                    <button
                      onClick={() => sendTaskCommand(task.id, 'start-sync')}
                      className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200"
                      disabled={client.status !== 'online'}
                    >
                      Start
                    </button>
                  )}

                  <button
                    onClick={() => sendTaskCommand(task.id, 'stop-sync')}
                    className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
                    disabled={client.status !== 'online'}
                  >
                    Stop
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Task Detail Modal */}
      {selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  Task Details: {selectedTask.name}
                </h2>
                <button 
                  onClick={() => setSelectedTask(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedTask.status)}`}>
                      {selectedTask.status}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Sync Type</label>
                    <p className="text-gray-900">{selectedTask.sync_type}</p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Source Path</label>
                  <p className="text-gray-900 font-mono text-sm bg-gray-50 p-2 rounded">{selectedTask.source_path}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Destination Path</label>
                  <p className="text-gray-900 font-mono text-sm bg-gray-50 p-2 rounded">{selectedTask.destination_path}</p>
                </div>

                {selectedTask.schedule && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Schedule</label>
                    <p className="text-gray-900">{selectedTask.schedule}</p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="text-gray-900">{new Date(selectedTask.created_at).toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Sync</label>
                    <p className="text-gray-900">
                      {selectedTask.last_sync ? new Date(selectedTask.last_sync).toLocaleString() : 'Never'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientTasks;
