{"name": "syncmasterpro", "version": "1.0.0", "description": "Professional File & Folder Synchronization Software", "main": "electron/main.js", "homepage": "./", "scripts": {"setup": "node scripts/setup.js", "start": "concurrently \"npm run server-desktop\" \"npm run dev-desktop\" \"npm run electron-dev\"", "electron-dev": "cross-env NODE_ENV=development electron .", "electron-pack": "electron-builder", "build": "react-scripts build", "server": "nodemon server/index.js", "server-web": "cross-env DB_TYPE=postgresql DB_HOST=************* DB_USER=pi DB_PASSWORD=ubuntu DB_NAME=syncmasterpro DB_PORT=5432 PORT=5001 CLIENT_URL=http://localhost:3001 ENABLE_DB_SYNC=true nodemon server/index.js", "server-desktop": "cross-env DB_TYPE=sqlite ELECTRON_ENV=true PORT=5002 WEB_SERVER_URL=http://localhost:5001 nodemon server/index.js", "start-desktop": "concurrently \"npm run server-desktop\" \"npm run dev-desktop\" \"npm run electron-dev\"", "dev-desktop": "cross-env REACT_APP_API_URL=http://localhost:5002/api PORT=3000 react-scripts start", "web-ui": "cd web-ui && npm start", "web-ui:build": "cd web-ui && npm run build", "web-ui:install": "cd web-ui && npm install", "start-web-management": "concurrently \"npm run server-web\" \"npm run web-ui\"", "build-web-management": "npm run web-ui:build", "test": "react-scripts test", "eject": "react-scripts eject", "postinstall": "npm run setup && electron-builder install-app-deps", "dist": "npm run build && electron-builder --publish=never", "deploy": "npm run build && electron-builder --publish=always", "build-web": "cross-env REACT_APP_API_URL=/api npm run build", "build-desktop": "cross-env REACT_APP_API_URL=http://localhost:5002/api npm run build", "package-web": "npm run build-web && node scripts/package-web.js", "package-desktop": "npm run build-desktop && electron-builder --publish=never", "create-tables": "node scripts/create-tables.js", "create-pg-client-tables": "node scripts/create-pg-client-tables.js", "db-sync": "node scripts/manage-database-sync.js", "db-sync-status": "node scripts/manage-database-sync.js status", "db-sync-start": "node scripts/manage-database-sync.js start", "db-sync-test": "node scripts/manage-database-sync.js test", "db-sync-compare": "node scripts/manage-database-sync.js compare"}, "keywords": ["file-sync", "folder-sync", "desktop-app", "electron", "react", "synchronization"], "author": "SyncMasterPro Team", "license": "MIT", "dependencies": {"@electron/remote": "^2.0.12", "@heroicons/react": "^2.0.18", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^11.10.0", "chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "electron-store": "^8.1.0", "express": "^4.18.2", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "pg": "^8.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.0", "archiver": "^6.0.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^27.1.3", "electron-builder": "^24.6.4", "electron-updater": "^6.6.2", "nodemon": "^3.0.2", "postcss": "^8.4.32", "react-scripts": "5.0.1", "tailwindcss": "^3.3.6"}, "build": {"appId": "com.syncmasterpro.app", "productName": "SyncMasterPro", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "server/**/*", "database/**/*", "core/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "electron/assets/icon.png"}, "win": {"target": "nsis", "icon": "electron/assets/icon.png"}, "linux": {"target": "AppImage", "icon": "electron/assets/icon.png"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": ">=2.0.1"}}