/**
 * SyncContext Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Manages sync operation state
 * - Encapsulation: Provides controlled access to sync data
 * - Immutability: Prevents external modification of critical data
 */
class SyncContext {
  constructor(task, options = {}) {
    this.task = { ...task }; // Immutable copy
    this.taskId = task.id;
    this.startTime = Date.now();
    this.endTime = null;
    this.status = 'initializing';
    
    // Statistics
    this.stats = {
      filesProcessed: 0,
      filesAdded: 0,
      filesUpdated: 0,
      filesDeleted: 0,
      filesSkipped: 0,
      totalSize: 0,
      bytesTransferred: 0,
      filesTotal: 0
    };
    
    // Error tracking
    this.errors = [];
    this.warnings = [];
    
    // Options
    this.options = {
      dryRun: false,
      verbose: false,
      ...options
    };
    
    // State flags
    this.cancelled = false;
    this.paused = false;
  }

  /**
   * Updates sync statistics
   * @param {Object} updates - Statistics to update
   */
  updateStats(updates) {
    Object.assign(this.stats, updates);
  }

  /**
   * Increments a specific statistic
   * @param {string} statName - Name of the statistic
   * @param {number} amount - Amount to increment (default: 1)
   */
  incrementStat(statName, amount = 1) {
    if (this.stats.hasOwnProperty(statName)) {
      this.stats[statName] += amount;
    }
  }

  /**
   * Adds an error to the context
   * @param {Error|string} error - Error to add
   * @param {string} context - Additional context information
   */
  addError(error, context = '') {
    const errorMessage = error instanceof Error ? error.message : error;
    this.errors.push({
      message: errorMessage,
      context,
      timestamp: Date.now()
    });
  }

  /**
   * Adds a warning to the context
   * @param {string} message - Warning message
   * @param {string} context - Additional context information
   */
  addWarning(message, context = '') {
    this.warnings.push({
      message,
      context,
      timestamp: Date.now()
    });
  }

  /**
   * Marks the sync as cancelled
   */
  cancel() {
    this.cancelled = true;
    this.status = 'cancelled';
  }

  /**
   * Pauses the sync operation
   */
  pause() {
    this.paused = true;
    this.status = 'paused';
  }

  /**
   * Resumes the sync operation
   */
  resume() {
    this.paused = false;
    this.status = 'running';
  }

  /**
   * Marks the sync as completed
   */
  complete() {
    this.endTime = Date.now();
    this.status = 'completed';
  }

  /**
   * Marks the sync as failed
   * @param {Error} error - The error that caused the failure
   */
  fail(error) {
    this.endTime = Date.now();
    this.status = 'failed';
    this.addError(error, 'Sync operation failed');
  }

  /**
   * Gets the current progress percentage
   * @returns {number} Progress percentage (0-100)
   */
  getProgress() {
    if (this.stats.filesTotal === 0) return 0;
    return Math.round((this.stats.filesProcessed / this.stats.filesTotal) * 100);
  }

  /**
   * Gets the duration of the sync operation
   * @returns {number} Duration in milliseconds
   */
  getDuration() {
    const endTime = this.endTime || Date.now();
    return endTime - this.startTime;
  }

  /**
   * Gets a summary of the sync operation
   * @returns {Object} Sync summary
   */
  getSummary() {
    return {
      taskId: this.taskId,
      taskName: this.task.name,
      status: this.status,
      duration: this.getDuration(),
      progress: this.getProgress(),
      stats: { ...this.stats },
      errorCount: this.errors.length,
      warningCount: this.warnings.length,
      cancelled: this.cancelled,
      startTime: this.startTime,
      endTime: this.endTime
    };
  }

  /**
   * Gets detailed results including errors and warnings
   * @returns {Object} Detailed sync results
   */
  getDetailedResults() {
    return {
      ...this.getSummary(),
      errors: [...this.errors],
      warnings: [...this.warnings],
      task: { ...this.task },
      options: { ...this.options }
    };
  }

  /**
   * Checks if the sync should continue
   * @returns {boolean} True if sync should continue
   */
  shouldContinue() {
    return !this.cancelled && !this.paused;
  }

  /**
   * Validates the sync context
   * @throws {Error} If context is invalid
   */
  validate() {
    if (!this.task || !this.task.id) {
      throw new Error('Invalid task: Task must have an ID');
    }

    if (!this.task.sourcePath && !this.task.source_path) {
      throw new Error('Invalid task: Source path is required');
    }

    if (!this.task.destinationPath && !this.task.destination_path) {
      throw new Error('Invalid task: Destination path is required');
    }
  }

  /**
   * Creates a progress update object for real-time updates
   * @returns {Object} Progress update object
   */
  createProgressUpdate() {
    return {
      taskId: this.taskId,
      status: this.status,
      progress: this.getProgress(),
      filesProcessed: this.stats.filesProcessed,
      filesTotal: this.stats.filesTotal,
      bytesTransferred: this.stats.bytesTransferred,
      currentFile: this.currentFile || null,
      timestamp: Date.now()
    };
  }

  /**
   * Sets the current file being processed
   * @param {string} filePath - Path of the current file
   */
  setCurrentFile(filePath) {
    this.currentFile = filePath;
  }
}

module.exports = SyncContext;
