/**
 * Validation Middleware
 * 
 * Clean Code principles:
 * - Single Responsibility: <PERSON>les input validation
 * - DRY: Reusable validation schemas
 * - Consistent Error Handling: Standardized validation responses
 * - Security: Input sanitization and validation
 */

/**
 * Creates a validation middleware for request body
 * @param {Object} schema - Validation schema
 * @returns {Function} Express middleware
 */
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = validateRequest(req.body, schema);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message,
        details: error.details
      });
    }
    
    req.body = value; // Use sanitized values
    next();
  };
};

/**
 * Creates a validation middleware for query parameters
 * @param {Object} schema - Validation schema
 * @returns {Function} Express middleware
 */
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = validateRequest(req.query, schema);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message,
        details: error.details
      });
    }
    
    req.query = value; // Use sanitized values
    next();
  };
};

/**
 * Creates a validation middleware for route parameters
 * @param {Object} schema - Validation schema
 * @returns {Function} Express middleware
 */
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error, value } = validateRequest(req.params, schema);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message,
        details: error.details
      });
    }
    
    req.params = value; // Use sanitized values
    next();
  };
};

/**
 * Validates request data against schema
 * @param {Object} data - Data to validate
 * @param {Object} schema - Validation schema
 * @returns {Object} Validation result
 */
const validateRequest = (data, schema) => {
  const errors = [];
  const sanitized = {};

  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];
    const fieldResult = validateField(field, value, rules);
    
    if (fieldResult.error) {
      errors.push(fieldResult.error);
    } else {
      sanitized[field] = fieldResult.value;
    }
  }

  if (errors.length > 0) {
    return {
      error: {
        message: 'Validation failed',
        details: errors
      }
    };
  }

  return { value: sanitized };
};

/**
 * Validates a single field
 * @param {string} fieldName - Field name
 * @param {*} value - Field value
 * @param {Object} rules - Validation rules
 * @returns {Object} Validation result
 */
const validateField = (fieldName, value, rules) => {
  // Handle required fields
  if (rules.required && (value === undefined || value === null || value === '')) {
    return {
      error: {
        field: fieldName,
        message: `${fieldName} is required`
      }
    };
  }

  // Skip validation if field is optional and not provided
  if (!rules.required && (value === undefined || value === null)) {
    return { value: rules.default || value };
  }

  // Type validation
  if (rules.type) {
    const typeResult = validateType(fieldName, value, rules.type);
    if (typeResult.error) {
      return typeResult;
    }
    value = typeResult.value;
  }

  // Length validation
  if (rules.minLength && value.length < rules.minLength) {
    return {
      error: {
        field: fieldName,
        message: `${fieldName} must be at least ${rules.minLength} characters long`
      }
    };
  }

  if (rules.maxLength && value.length > rules.maxLength) {
    return {
      error: {
        field: fieldName,
        message: `${fieldName} must be no more than ${rules.maxLength} characters long`
      }
    };
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    return {
      error: {
        field: fieldName,
        message: rules.patternMessage || `${fieldName} format is invalid`
      }
    };
  }

  // Enum validation
  if (rules.enum && !rules.enum.includes(value)) {
    return {
      error: {
        field: fieldName,
        message: `${fieldName} must be one of: ${rules.enum.join(', ')}`
      }
    };
  }

  // Custom validation
  if (rules.validate) {
    const customResult = rules.validate(value);
    if (customResult !== true) {
      return {
        error: {
          field: fieldName,
          message: customResult || `${fieldName} is invalid`
        }
      };
    }
  }

  return { value: rules.transform ? rules.transform(value) : value };
};

/**
 * Validates field type
 * @param {string} fieldName - Field name
 * @param {*} value - Field value
 * @param {string} expectedType - Expected type
 * @returns {Object} Validation result
 */
const validateType = (fieldName, value, expectedType) => {
  switch (expectedType) {
    case 'string':
      if (typeof value !== 'string') {
        return {
          error: {
            field: fieldName,
            message: `${fieldName} must be a string`
          }
        };
      }
      return { value: value.trim() };

    case 'number':
      const num = Number(value);
      if (isNaN(num)) {
        return {
          error: {
            field: fieldName,
            message: `${fieldName} must be a number`
          }
        };
      }
      return { value: num };

    case 'integer':
      const int = parseInt(value);
      if (isNaN(int) || int.toString() !== value.toString()) {
        return {
          error: {
            field: fieldName,
            message: `${fieldName} must be an integer`
          }
        };
      }
      return { value: int };

    case 'boolean':
      if (typeof value === 'boolean') {
        return { value };
      }
      if (value === 'true' || value === '1') {
        return { value: true };
      }
      if (value === 'false' || value === '0') {
        return { value: false };
      }
      return {
        error: {
          field: fieldName,
          message: `${fieldName} must be a boolean`
        }
      };

    case 'array':
      if (!Array.isArray(value)) {
        return {
          error: {
            field: fieldName,
            message: `${fieldName} must be an array`
          }
        };
      }
      return { value };

    case 'object':
      if (typeof value !== 'object' || Array.isArray(value)) {
        return {
          error: {
            field: fieldName,
            message: `${fieldName} must be an object`
          }
        };
      }
      return { value };

    default:
      return { value };
  }
};

// Common validation schemas
const schemas = {
  // User registration schema
  userRegistration: {
    email: {
      type: 'string',
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      patternMessage: 'Invalid email format',
      transform: (value) => value.toLowerCase().trim()
    },
    password: {
      type: 'string',
      required: true,
      minLength: 8,
      validate: (value) => {
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
        }
        return true;
      }
    },
    name: {
      type: 'string',
      required: true,
      minLength: 2,
      maxLength: 100,
      transform: (value) => value.trim()
    }
  },

  // User login schema
  userLogin: {
    email: {
      type: 'string',
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      patternMessage: 'Invalid email format',
      transform: (value) => value.toLowerCase().trim()
    },
    password: {
      type: 'string',
      required: true,
      minLength: 1
    }
  },

  // Sync task creation schema
  createSyncTask: {
    name: {
      type: 'string',
      required: true,
      minLength: 3,
      maxLength: 255,
      transform: (value) => value.trim()
    },
    sourcePath: {
      type: 'string',
      required: true,
      minLength: 1,
      transform: (value) => value.trim()
    },
    destinationPath: {
      type: 'string',
      required: true,
      minLength: 1,
      transform: (value) => value.trim()
    },
    syncType: {
      type: 'string',
      required: false,
      enum: ['bidirectional', 'source-to-destination', 'destination-to-source', 'mirror', 'incremental', 'today-only'],
      default: 'bidirectional'
    },
    schedule: {
      type: 'string',
      required: false
    },
    filters: {
      type: 'array',
      required: false,
      default: []
    },
    options: {
      type: 'object',
      required: false,
      default: {}
    }
  },

  // Task ID parameter schema
  taskId: {
    id: {
      type: 'integer',
      required: true,
      validate: (value) => value > 0 || 'Task ID must be a positive integer'
    }
  },

  // Pagination query schema
  pagination: {
    limit: {
      type: 'integer',
      required: false,
      default: 50,
      validate: (value) => (value > 0 && value <= 100) || 'Limit must be between 1 and 100'
    },
    offset: {
      type: 'integer',
      required: false,
      default: 0,
      validate: (value) => value >= 0 || 'Offset must be non-negative'
    }
  }
};

module.exports = {
  validateBody,
  validateQuery,
  validateParams,
  schemas
};
