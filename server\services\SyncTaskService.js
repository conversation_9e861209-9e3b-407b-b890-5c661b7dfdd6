const repositoryFactory = require('../repositories/RepositoryFactory');
const path = require('path');
const fs = require('fs-extra');

/**
 * SyncTaskService Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles sync task business logic
 * - Dependency Injection: Uses repositories for data access
 * - Validation: Comprehensive input validation
 * - Error Handling: Consistent error handling patterns
 */
class SyncTaskService {
  constructor() {
    this.syncTaskRepository = repositoryFactory.getSyncTaskRepository();
    this.syncHistoryRepository = repositoryFactory.getSyncHistoryRepository();
    this.userRepository = repositoryFactory.getUserRepository();
  }

  /**
   * Creates a new sync task
   * @param {Object} taskData - Task data
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Creation result
   */
  async createTask(taskData, userId) {
    try {
      // Validate task data
      await this._validateTaskData(taskData);

      // Validate paths
      await this._validatePaths(taskData.source_path, taskData.destination_path);

      // Prepare task data
      const taskToCreate = {
        ...taskData,
        user_id: userId,
        name: taskData.name.trim(),
        source_path: path.resolve(taskData.source_path),
        destination_path: path.resolve(taskData.destination_path),
        sync_type: taskData.sync_type || 'bidirectional',
        filters: taskData.filters || [],
        options: taskData.options || {},
        status: 'idle'
      };

      // Create task
      const task = await this.syncTaskRepository.createTask(taskToCreate);

      return {
        success: true,
        task,
        message: 'Sync task created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Updates a sync task
   * @param {number} taskId - Task ID
   * @param {Object} updateData - Data to update
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Update result
   */
  async updateTask(taskId, updateData, userId) {
    try {
      // Check if task exists and belongs to user
      const existingTask = await this.syncTaskRepository.findOne({
        id: taskId,
        user_id: userId
      });

      if (!existingTask) {
        throw new Error('Task not found or access denied');
      }

      // Validate update data
      if (updateData.source_path || updateData.destination_path) {
        await this._validatePaths(
          updateData.source_path || existingTask.source_path,
          updateData.destination_path || existingTask.destination_path
        );
      }

      // Prepare update data
      const dataToUpdate = { ...updateData };
      
      if (dataToUpdate.name) {
        dataToUpdate.name = dataToUpdate.name.trim();
      }

      if (dataToUpdate.source_path) {
        dataToUpdate.source_path = path.resolve(dataToUpdate.source_path);
      }

      if (dataToUpdate.destination_path) {
        dataToUpdate.destination_path = path.resolve(dataToUpdate.destination_path);
      }

      // Update task
      const task = await this.syncTaskRepository.updateTask(taskId, dataToUpdate);

      return {
        success: true,
        task,
        message: 'Sync task updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Gets user's sync tasks
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Tasks result
   */
  async getUserTasks(userId, options = {}) {
    try {
      const tasks = await this.syncTaskRepository.getTasksByUser(userId, options);
      const stats = await this.syncTaskRepository.getTaskStats(userId);

      return {
        success: true,
        tasks,
        stats
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Gets a specific task with details
   * @param {number} taskId - Task ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Task details result
   */
  async getTaskDetails(taskId, userId) {
    try {
      const task = await this.syncTaskRepository.getTaskWithDetails(taskId, userId);
      
      if (!task) {
        throw new Error('Task not found or access denied');
      }

      // Get recent history for this task
      const recentHistory = await this.syncHistoryRepository.getHistoryByTask(taskId, {
        limit: 10
      });

      return {
        success: true,
        task: {
          ...task,
          recentHistory
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Deletes a sync task
   * @param {number} taskId - Task ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteTask(taskId, userId) {
    try {
      const success = await this.syncTaskRepository.deleteTask(taskId, userId);
      
      if (!success) {
        throw new Error('Task not found or access denied');
      }

      return {
        success: true,
        message: 'Sync task deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Searches tasks by name or path
   * @param {string} searchTerm - Search term
   * @param {number} userId - User ID
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async searchTasks(searchTerm, userId, options = {}) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long');
      }

      const tasks = await this.syncTaskRepository.searchTasks(
        searchTerm.trim(), 
        userId, 
        options
      );

      return {
        success: true,
        tasks,
        searchTerm: searchTerm.trim()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Updates task status
   * @param {number} taskId - Task ID
   * @param {string} status - New status
   * @param {number} userId - User ID
   * @param {Object} additionalData - Additional data
   * @returns {Promise<Object>} Update result
   */
  async updateTaskStatus(taskId, status, userId, additionalData = {}) {
    try {
      // Validate status
      const validStatuses = ['idle', 'running', 'completed', 'failed', 'cancelled', 'paused'];
      if (!validStatuses.includes(status)) {
        throw new Error('Invalid status');
      }

      // Check if task belongs to user
      const existingTask = await this.syncTaskRepository.findOne({
        id: taskId,
        user_id: userId
      });

      if (!existingTask) {
        throw new Error('Task not found or access denied');
      }

      // Update task status
      const task = await this.syncTaskRepository.updateTaskStatus(
        taskId, 
        status, 
        additionalData
      );

      return {
        success: true,
        task,
        message: `Task status updated to ${status}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Gets scheduled tasks that need to run
   * @returns {Promise<Object>} Scheduled tasks result
   */
  async getScheduledTasks() {
    try {
      const tasks = await this.syncTaskRepository.getScheduledTasks();

      return {
        success: true,
        tasks
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validates task data
   * @param {Object} taskData - Task data to validate
   * @private
   */
  async _validateTaskData(taskData) {
    const { name, source_path, destination_path, sync_type } = taskData;

    // Required fields
    if (!name || !source_path || !destination_path) {
      throw new Error('Name, source path, and destination path are required');
    }

    // Name validation
    if (name.trim().length < 3) {
      throw new Error('Task name must be at least 3 characters long');
    }

    // Sync type validation
    const validSyncTypes = [
      'bidirectional', 
      'source-to-destination', 
      'destination-to-source',
      'mirror',
      'incremental',
      'today-only'
    ];
    
    if (sync_type && !validSyncTypes.includes(sync_type)) {
      throw new Error('Invalid sync type');
    }

    // Path validation
    if (source_path === destination_path) {
      throw new Error('Source and destination paths cannot be the same');
    }
  }

  /**
   * Validates that paths exist and are accessible
   * @param {string} sourcePath - Source path
   * @param {string} destinationPath - Destination path
   * @private
   */
  async _validatePaths(sourcePath, destinationPath) {
    try {
      // Check if source path exists
      const sourceExists = await fs.pathExists(sourcePath);
      if (!sourceExists) {
        throw new Error('Source path does not exist');
      }

      // Check if source is a directory
      const sourceStats = await fs.stat(sourcePath);
      if (!sourceStats.isDirectory()) {
        throw new Error('Source path must be a directory');
      }

      // Ensure destination directory exists or can be created
      await fs.ensureDir(destinationPath);

      // Check if destination is a directory
      const destStats = await fs.stat(destinationPath);
      if (!destStats.isDirectory()) {
        throw new Error('Destination path must be a directory');
      }

      // Check for nested paths (source inside destination or vice versa)
      const resolvedSource = path.resolve(sourcePath);
      const resolvedDest = path.resolve(destinationPath);
      
      if (resolvedSource.startsWith(resolvedDest) || resolvedDest.startsWith(resolvedSource)) {
        throw new Error('Source and destination paths cannot be nested within each other');
      }

    } catch (error) {
      if (error.code === 'EACCES') {
        throw new Error('Permission denied accessing the specified paths');
      }
      throw error;
    }
  }
}

module.exports = SyncTaskService;
