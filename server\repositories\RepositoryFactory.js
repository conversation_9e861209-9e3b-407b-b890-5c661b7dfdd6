const UserRepository = require('./UserRepository');
const SyncTaskRepository = require('./SyncTaskRepository');
const SyncHistoryRepository = require('./SyncHistoryRepository');

/**
 * RepositoryFactory Class
 * 
 * Clean Code principles:
 * - Factory Pattern: Centralized repository creation
 * - Singleton Pattern: Ensures single instance of each repository
 * - Dependency Injection: Provides repositories to services
 * - Separation of Concerns: Isolates repository management
 */
class RepositoryFactory {
  constructor() {
    this.repositories = new Map();
  }

  /**
   * Gets or creates a repository instance
   * @param {string} repositoryName - Name of the repository
   * @returns {Object} Repository instance
   */
  getRepository(repositoryName) {
    if (!this.repositories.has(repositoryName)) {
      this.repositories.set(repositoryName, this._createRepository(repositoryName));
    }
    return this.repositories.get(repositoryName);
  }

  /**
   * Gets user repository
   * @returns {UserRepository} User repository instance
   */
  getUserRepository() {
    return this.getRepository('user');
  }

  /**
   * Gets sync task repository
   * @returns {SyncTaskRepository} Sync task repository instance
   */
  getSyncTaskRepository() {
    return this.getRepository('syncTask');
  }

  /**
   * Gets sync history repository
   * @returns {SyncHistoryRepository} Sync history repository instance
   */
  getSyncHistoryRepository() {
    return this.getRepository('syncHistory');
  }

  /**
   * Gets all available repositories
   * @returns {Object} Object containing all repositories
   */
  getAllRepositories() {
    return {
      user: this.getUserRepository(),
      syncTask: this.getSyncTaskRepository(),
      syncHistory: this.getSyncHistoryRepository()
    };
  }

  /**
   * Clears all repository instances (useful for testing)
   */
  clearRepositories() {
    this.repositories.clear();
  }

  /**
   * Creates a repository instance based on name
   * @param {string} repositoryName - Name of the repository
   * @returns {Object} Repository instance
   * @private
   */
  _createRepository(repositoryName) {
    switch (repositoryName) {
      case 'user':
        return new UserRepository();
      case 'syncTask':
        return new SyncTaskRepository();
      case 'syncHistory':
        return new SyncHistoryRepository();
      default:
        throw new Error(`Unknown repository: ${repositoryName}`);
    }
  }
}

// Export singleton instance
const repositoryFactory = new RepositoryFactory();

module.exports = repositoryFactory;
