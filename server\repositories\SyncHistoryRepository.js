const BaseRepository = require('./BaseRepository');

/**
 * SyncHistoryRepository Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles sync history database operations
 * - Data Integrity: Ensures proper history tracking
 * - Performance: Optimized queries for history retrieval
 */
class SyncHistoryRepository extends BaseRepository {
  constructor() {
    super('sync_history', 'id');
  }

  /**
   * Creates a new sync history record
   * @param {Object} historyData - History data
   * @returns {Promise<Object>} Created history record
   */
  async createHistory(historyData) {
    try {
      const historyToCreate = {
        ...historyData,
        details: JSON.stringify(historyData.details || {}),
        started_at: historyData.started_at || new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      const history = await this.create(historyToCreate);
      return this._deserializeHistory(history);
    } catch (error) {
      throw new Error(`Failed to create sync history: ${error.message}`);
    }
  }

  /**
   * Updates sync history record
   * @param {number} historyId - History ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated history record
   */
  async updateHistory(historyId, updateData) {
    try {
      const dataToUpdate = { ...updateData };
      
      // Serialize details if provided
      if (dataToUpdate.details) {
        dataToUpdate.details = JSON.stringify(dataToUpdate.details);
      }

      const history = await this.update(historyId, dataToUpdate);
      return history ? this._deserializeHistory(history) : null;
    } catch (error) {
      throw new Error(`Failed to update sync history: ${error.message}`);
    }
  }

  /**
   * Gets sync history for a task
   * @param {number} taskId - Task ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Sync history records
   */
  async getHistoryByTask(taskId, options = {}) {
    try {
      const { limit = 50, offset = 0 } = options;
      
      const sql = `
        SELECT 
          sh.*,
          st.name as task_name,
          st.sync_type
        FROM sync_history sh
        JOIN sync_tasks st ON sh.task_id = st.id
        WHERE sh.task_id = ?
        ORDER BY sh.started_at DESC
        LIMIT ? OFFSET ?
      `;

      const result = await this.db.query(sql, [taskId, limit, offset]);
      const histories = this._getAllRows(result);
      return histories.map(history => this._deserializeHistory(history));
    } catch (error) {
      throw new Error(`Failed to get history for task ${taskId}: ${error.message}`);
    }
  }

  /**
   * Gets sync history for a user
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} History records with pagination
   */
  async getHistoryByUser(userId, options = {}) {
    try {
      const { 
        limit = 50, 
        offset = 0, 
        status = null,
        taskId = null,
        dateFrom = null,
        dateTo = null
      } = options;

      // Build WHERE clause
      let whereClause = 'WHERE sh.user_id = ?';
      const params = [userId];

      if (status) {
        whereClause += ' AND sh.status = ?';
        params.push(status);
      }

      if (taskId) {
        whereClause += ' AND sh.task_id = ?';
        params.push(taskId);
      }

      if (dateFrom) {
        whereClause += ' AND sh.started_at >= ?';
        params.push(dateFrom);
      }

      if (dateTo) {
        whereClause += ' AND sh.started_at <= ?';
        params.push(dateTo);
      }

      // Get total count
      const countSql = `
        SELECT COUNT(*) as count 
        FROM sync_history sh 
        ${whereClause}
      `;
      const countResult = await this.db.query(countSql, params);
      const total = this._getFirstRow(countResult).count;

      // Get history records
      const historySql = `
        SELECT 
          sh.*,
          st.name as task_name,
          st.sync_type,
          st.source_path,
          st.destination_path
        FROM sync_history sh
        JOIN sync_tasks st ON sh.task_id = st.id
        ${whereClause}
        ORDER BY sh.started_at DESC
        LIMIT ? OFFSET ?
      `;

      const historyResult = await this.db.query(historySql, [...params, limit, offset]);
      const histories = this._getAllRows(historyResult);

      return {
        histories: histories.map(history => this._deserializeHistory(history)),
        pagination: {
          total,
          limit,
          offset,
          pages: Math.ceil(total / limit),
          currentPage: Math.floor(offset / limit) + 1
        }
      };
    } catch (error) {
      throw new Error(`Failed to get history for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Gets recent sync history
   * @param {number} userId - User ID
   * @param {number} limit - Number of records to return
   * @returns {Promise<Array>} Recent sync history
   */
  async getRecentHistory(userId, limit = 10) {
    try {
      const sql = `
        SELECT 
          sh.*,
          st.name as task_name,
          st.sync_type
        FROM sync_history sh
        JOIN sync_tasks st ON sh.task_id = st.id
        WHERE sh.user_id = ?
        ORDER BY sh.started_at DESC
        LIMIT ?
      `;

      const result = await this.db.query(sql, [userId, limit]);
      const histories = this._getAllRows(result);
      return histories.map(history => this._deserializeHistory(history));
    } catch (error) {
      throw new Error(`Failed to get recent history: ${error.message}`);
    }
  }

  /**
   * Gets sync statistics for a time period
   * @param {number} userId - User ID
   * @param {string} period - Time period ('day', 'week', 'month', 'year')
   * @returns {Promise<Object>} Sync statistics
   */
  async getSyncStats(userId, period = 'month') {
    try {
      const dateFilter = this._getDateFilter(period);
      
      const sql = `
        SELECT
          COUNT(*) as total_syncs,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_syncs,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_syncs,
          COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_syncs,
          SUM(files_count) as total_files,
          SUM(total_size) as total_bytes,
          AVG(CASE WHEN ended_at IS NOT NULL AND started_at IS NOT NULL 
              THEN (julianday(ended_at) - julianday(started_at)) * 86400 
              END) as avg_duration_seconds
        FROM sync_history
        WHERE user_id = ? AND started_at >= ?
      `;

      const result = await this.db.query(sql, [userId, dateFilter]);
      const stats = this._getFirstRow(result);

      return {
        totalSyncs: stats.total_syncs || 0,
        successfulSyncs: stats.successful_syncs || 0,
        failedSyncs: stats.failed_syncs || 0,
        cancelledSyncs: stats.cancelled_syncs || 0,
        totalFiles: stats.total_files || 0,
        totalBytes: stats.total_bytes || 0,
        avgDurationSeconds: stats.avg_duration_seconds || 0,
        successRate: stats.total_syncs > 0 
          ? Math.round((stats.successful_syncs / stats.total_syncs) * 100) 
          : 0
      };
    } catch (error) {
      throw new Error(`Failed to get sync stats: ${error.message}`);
    }
  }

  /**
   * Gets sync history grouped by date
   * @param {number} userId - User ID
   * @param {number} days - Number of days to include
   * @returns {Promise<Array>} Daily sync statistics
   */
  async getDailySyncStats(userId, days = 30) {
    try {
      const sql = `
        SELECT
          DATE(started_at) as sync_date,
          COUNT(*) as total_syncs,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_syncs,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_syncs,
          SUM(files_count) as files_synced,
          SUM(total_size) as bytes_synced
        FROM sync_history
        WHERE user_id = ? 
        AND started_at >= date('now', '-${days} days')
        GROUP BY DATE(started_at)
        ORDER BY sync_date DESC
      `;

      const result = await this.db.query(sql, [userId]);
      return this._getAllRows(result);
    } catch (error) {
      throw new Error(`Failed to get daily sync stats: ${error.message}`);
    }
  }

  /**
   * Deletes old sync history records
   * @param {number} daysToKeep - Number of days to keep
   * @returns {Promise<number>} Number of deleted records
   */
  async cleanupOldHistory(daysToKeep = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const sql = `
        DELETE FROM sync_history 
        WHERE started_at < ?
      `;

      const result = await this.db.query(sql, [cutoffDate.toISOString()]);
      return result.rowCount || result.changes || 0;
    } catch (error) {
      throw new Error(`Failed to cleanup old history: ${error.message}`);
    }
  }

  /**
   * Gets the latest sync for each task
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Latest sync for each task
   */
  async getLatestSyncPerTask(userId) {
    try {
      const sql = `
        SELECT 
          sh.*,
          st.name as task_name,
          st.sync_type
        FROM sync_history sh
        JOIN sync_tasks st ON sh.task_id = st.id
        WHERE sh.user_id = ?
        AND sh.started_at = (
          SELECT MAX(started_at) 
          FROM sync_history sh2 
          WHERE sh2.task_id = sh.task_id
        )
        ORDER BY sh.started_at DESC
      `;

      const result = await this.db.query(sql, [userId]);
      const histories = this._getAllRows(result);
      return histories.map(history => this._deserializeHistory(history));
    } catch (error) {
      throw new Error(`Failed to get latest sync per task: ${error.message}`);
    }
  }

  /**
   * Gets date filter for time period
   * @param {string} period - Time period
   * @returns {string} ISO date string
   */
  _getDateFilter(period) {
    const now = new Date();
    
    switch (period) {
      case 'day':
        now.setDate(now.getDate() - 1);
        break;
      case 'week':
        now.setDate(now.getDate() - 7);
        break;
      case 'month':
        now.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        now.setFullYear(now.getFullYear() - 1);
        break;
      default:
        now.setMonth(now.getMonth() - 1);
    }
    
    return now.toISOString();
  }

  /**
   * Deserializes JSON fields in history object
   * @param {Object} history - Raw history from database
   * @returns {Object} History with parsed JSON fields
   */
  _deserializeHistory(history) {
    if (!history) return null;

    const deserializedHistory = { ...history };

    // Parse details field
    if (typeof history.details === 'string') {
      try {
        deserializedHistory.details = JSON.parse(history.details);
      } catch (error) {
        deserializedHistory.details = {};
      }
    }

    return deserializedHistory;
  }
}

module.exports = SyncHistoryRepository;
