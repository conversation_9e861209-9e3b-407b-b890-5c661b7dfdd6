const repositoryFactory = require('../repositories/RepositoryFactory');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

/**
 * UserService Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles user business logic
 * - Dependency Injection: Uses repository for data access
 * - Separation of Concerns: Separates business logic from data access
 * - Error Handling: Consistent error handling patterns
 */
class UserService {
  constructor() {
    this.userRepository = repositoryFactory.getUserRepository();
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
  }

  /**
   * Registers a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  async registerUser(userData) {
    try {
      // Validate required fields
      this._validateRegistrationData(userData);

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Create user
      const user = await this.userRepository.createUser({
        email: userData.email.toLowerCase().trim(),
        password: userData.password,
        name: userData.name.trim(),
        role: userData.role || 'user',
        status: userData.status || 'active'
      });

      // Generate JWT token
      const token = this._generateToken(user);

      return {
        success: true,
        user,
        token,
        message: 'User registered successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Authenticates a user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} Authentication result
   */
  async authenticateUser(email, password) {
    try {
      // Validate input
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // Validate credentials
      const user = await this.userRepository.validateCredentials(
        email.toLowerCase().trim(), 
        password
      );

      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check if user is active
      if (user.status !== 'active') {
        throw new Error('Account is not active');
      }

      // Generate JWT token
      const token = this._generateToken(user);

      return {
        success: true,
        user,
        token,
        message: 'Authentication successful'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Gets user profile by ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User profile
   */
  async getUserProfile(userId) {
    try {
      const user = await this.userRepository.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }

      // Get user statistics
      const stats = await this.userRepository.getUserStats(userId);

      return {
        success: true,
        user: {
          ...user,
          stats
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Updates user profile
   * @param {number} userId - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Update result
   */
  async updateUserProfile(userId, updateData) {
    try {
      // Validate update data
      this._validateProfileUpdateData(updateData);

      // Update user
      const user = await this.userRepository.updateProfile(userId, updateData);
      
      if (!user) {
        throw new Error('User not found');
      }

      return {
        success: true,
        user,
        message: 'Profile updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Changes user password
   * @param {number} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Password change result
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Get user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate current password
      const isValidPassword = await this.userRepository.validateCredentials(
        user.email, 
        currentPassword
      );
      
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }

      // Validate new password
      this._validatePassword(newPassword);

      // Update password
      const success = await this.userRepository.updatePassword(userId, newPassword);
      
      if (!success) {
        throw new Error('Failed to update password');
      }

      return {
        success: true,
        message: 'Password changed successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Gets users with pagination and filters (admin only)
   * @param {Object} filters - Filter options
   * @param {number} requestingUserId - ID of user making request
   * @returns {Promise<Object>} Users list result
   */
  async getUsers(filters, requestingUserId) {
    try {
      // Check if requesting user is admin
      const requestingUser = await this.userRepository.findById(requestingUserId);
      if (!requestingUser || requestingUser.role !== 'admin') {
        throw new Error('Insufficient permissions');
      }

      const result = await this.userRepository.getUsersWithPagination(filters);

      return {
        success: true,
        ...result
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Deactivates a user account
   * @param {number} userId - User ID to deactivate
   * @param {number} requestingUserId - ID of user making request
   * @returns {Promise<Object>} Deactivation result
   */
  async deactivateUser(userId, requestingUserId) {
    try {
      // Check permissions
      const requestingUser = await this.userRepository.findById(requestingUserId);
      if (!requestingUser || (requestingUser.role !== 'admin' && requestingUser.id !== userId)) {
        throw new Error('Insufficient permissions');
      }

      // Update user status
      const user = await this.userRepository.updateProfile(userId, {
        status: 'inactive'
      });

      if (!user) {
        throw new Error('User not found');
      }

      return {
        success: true,
        message: 'User deactivated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validates registration data
   * @param {Object} userData - User data to validate
   * @private
   */
  _validateRegistrationData(userData) {
    const { email, password, name } = userData;

    if (!email || !password || !name) {
      throw new Error('Email, password, and name are required');
    }

    if (!this._isValidEmail(email)) {
      throw new Error('Invalid email format');
    }

    this._validatePassword(password);

    if (name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters long');
    }
  }

  /**
   * Validates profile update data
   * @param {Object} updateData - Data to validate
   * @private
   */
  _validateProfileUpdateData(updateData) {
    if (updateData.email && !this._isValidEmail(updateData.email)) {
      throw new Error('Invalid email format');
    }

    if (updateData.name && updateData.name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters long');
    }
  }

  /**
   * Validates password strength
   * @param {string} password - Password to validate
   * @private
   */
  _validatePassword(password) {
    if (!password || password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      throw new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number');
    }
  }

  /**
   * Validates email format
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid
   * @private
   */
  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generates JWT token for user
   * @param {Object} user - User object
   * @returns {string} JWT token
   * @private
   */
  _generateToken(user) {
    return jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        role: user.role 
      },
      this.jwtSecret,
      { expiresIn: this.jwtExpiresIn }
    );
  }
}

module.exports = UserService;
