# 📁 Hybrid Folder Picker Guide

## Tổng quan

SyncMasterPro sử dụng **Hybrid Folder Picker** - một gi<PERSON>i pháp kết hợp 2 loại
folder picker để cung cấp trải nghiệm tốt nhất cho cả desktop và web app.

## 🎯 Hai loại Folder Picker

### 1. 🗂️ Native Windows Folder Picker

- **Mô tả**: Sử dụng dialog chọn thư mục gốc của Windows
- **Hoạt động**: Chỉ trong Electron desktop app
- **Ưu điểm**:
  - Giao diện quen thuộc
  - Tốc độ nhanh
  - Tích hợp hệ thống tốt
- **Nhược điểm**:
  - Chỉ hoạt động trên desktop
  - Không tùy chỉnh được

### 2. 🌐 Web-based Folder Picker

- **Mô tả**: Component React tùy chỉnh với API backend
- **Hoạt động**: Trên cả web và desktop app
- **Ưu điểm**:
  - Cross-platform
  - Giao diện nhất quán
  - <PERSON><PERSON> thể tùy chỉnh
- **<PERSON><PERSON><PERSON><PERSON><PERSON> điểm**:
  - Cần API calls
  - Chậm hơn native picker

## 🔧 Cách triển khai

### Trong CreateTaskModal.js

```javascript
// States để quản lý folder pickers
const [showSourcePicker, setShowSourcePicker] = useState(false);
const [showDestinationPicker, setShowDestinationPicker] = useState(false);

// Handler cho native picker
const handleNativeFolderPicker = async (type) => {
  if (window.electronAPI) {
    const result = await window.electronAPI.selectFolder();
    if (!result.canceled && result.filePaths.length > 0) {
      handleChange(type === 'source' ? 'sourcePath' : 'destinationPath', result.filePaths[0]);
    }
  }
};

// Handler cho web picker
const handleFolderSelect = (path, type) => {
  handleChange(type === 'source' ? 'sourcePath' : 'destinationPath', path);
  if (type === 'source') setShowSourcePicker(false);
  else setShowDestinationPicker(false);
};
```

### UI Layout

```javascript
{/* Native Windows Picker (Desktop only) */}
{window.electronAPI && (
  <button onClick={() => handleNativeFolderPicker('source')}>
    🗂️ Native
  </button>
)}

{/* Web-based Picker (Always available) */}
<button onClick={() => setShowSourcePicker(true)}>
  📁 Browse
</button>

{/* Folder Picker Modals */}
<FolderPicker
  isOpen={showSourcePicker}
  onClose={() => setShowSourcePicker(false)}
  onSelect={(path) => handleFolderSelect(path, 'source')}
  title="Select Source Folder"
/>
```

## 🎨 Giao diện người dùng

### Desktop App (Electron)

- **2 nút cho mỗi path field**:
  - `🗂️ Native` - Mở Windows dialog
  - `📁 Browse` - Mở web picker
- **Màu sắc**:
  - Source: Blue theme
  - Destination: Green theme

### Web App

- **1 nút cho mỗi path field**:
  - `📁 Browse` - Mở web picker
- **Native button ẩn** khi không có `window.electronAPI`

## 🔄 Luồng hoạt động

### 1. Desktop App Flow

```text
User clicks "Native" → Windows Dialog → Path selected → Input updated
     OR
User clicks "Browse" → Web Picker Modal → Navigate folders → Select → Input updated
```

### 2. Web App Flow

```text
User clicks "Browse" → Web Picker Modal → Navigate folders → Select → Input updated
```

## 🧪 Testing

### Chạy test script

```javascript
// Trong browser console (F12)
// Load test script
const script = document.createElement('script');
script.src = '/scripts/test-hybrid-folder-picker.js';
document.head.appendChild(script);

// Hoặc chạy manual tests
window.testHybridFolderPicker.runAllTests();
```

### Test cases

1. **Picker Availability**: Kiểm tra cả 2 loại picker có sẵn không
1. **Native Picker**: Test Windows dialog (desktop only)
1. **Web Picker API**: Test API `/sync/directories`
1. **UI Integration**: Kiểm tra buttons và modals
1. **Performance**: So sánh tốc độ và UX

## 🐛 Troubleshooting

### Vấn đề thường gặp

#### 1. Native picker không hoạt động

```text
Nguyên nhân: window.electronAPI không có
Giải pháp: Kiểm tra preload.js và main.js
```

#### 2. Web picker API lỗi

```text
Nguyên nhân: Không có token hoặc server không chạy
Giải pháp: Login lại và kiểm tra server
```

#### 3. Folder picker modal không hiện

```text
Nguyên nhân: State management hoặc z-index
Giải pháp: Kiểm tra useState và CSS z-index
```

## 📊 API Endpoints

### GET /api/sync/directories

```javascript
// Get home directory
GET /api/sync/directories

// Get specific directory
GET /api/sync/directories?path=C:\Users

// Response format
{
  "currentPath": "C:\\Users",
  "directories": [
    {
      "name": "Documents",
      "path": "C:\\Users\\<USER>