const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');

// Load environment variables
dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: [
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:5001",
      process.env.CLIENT_URL
    ].filter(Boolean),
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Routes will be imported after database initialization

// Import middleware
const authMiddleware = require('./middleware/auth');

// Import database
const { initializeDatabase } = require('./database/init');



// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes will be set up after database initialization

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'SyncMasterPro Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    database: process.env.DB_TYPE || 'sqlite',
    features: {
      tokenReuse: true,
      clientStateManager: !!app.get('clientStateManager'),
      databaseSync: !!app.get('dbSyncService'),
      realTimeSync: !!app.get('realTimeDatabaseSync')
    }
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build/index.html'));
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: err.message,
      details: err.details
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
  }
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler will be added after routes are setup

// Socket.IO authentication middleware
io.use((socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    console.log('🔐 Socket.IO auth attempt:', token ? 'Token present' : 'No token');

    if (!token) {
      console.log('⚠️ Socket.IO: No token provided, allowing connection anyway');
      return next();
    }

    // For now, allow all connections - we can add JWT verification later
    console.log('✅ Socket.IO: Authentication passed');
    next();
  } catch (error) {
    console.error('❌ Socket.IO auth error:', error);
    next(); // Allow connection anyway for now
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('✅ Socket.IO client connected:', socket.id);
  console.log('🔗 Client handshake:', {
    address: socket.handshake.address,
    headers: socket.handshake.headers.origin,
    auth: socket.handshake.auth ? 'Present' : 'Missing'
  });
  
  // Join user room for personalized updates
  socket.on('join-user-room', (userId) => {
    socket.join(`user-${userId}`);
    console.log(`User ${userId} joined room`);
  });

  // Handle client registration (for desktop clients)
  socket.on('client-register', async (data) => {
    try {
      const { clientId, userId } = data;
      console.log(`🖥️ Desktop client socket registered: ${clientId} for user ${userId}`);

      // Join client-specific room
      socket.join(`client-${clientId}`);
      socket.join(`user-${userId}`);

      // Update client status using state manager
      const clientStateManager = socket.request.app?.get('clientStateManager');

      if (clientStateManager) {
        console.log(`🔄 Forcing client status update via socket registration: ${clientId}`);

        const updateSuccess = await clientStateManager.updateClientStatus(clientId, 'online', {
          socketConnected: true,
          socketId: socket.id,
          lastSocketConnection: new Date(),
          registrationMethod: 'socket',
          forceUpdate: true // Force update even with token reuse
        });

        if (updateSuccess) {
          console.log(`✅ Client status updated via state manager: ${clientId} → online`);

          // Store client info in socket for disconnect handling
          socket.clientId = clientId;
          socket.userId = userId;

          // Broadcast status change to web dashboard
          socket.to(`user-${userId}`).emit('client-connected', {
            clientId,
            status: 'online',
            timestamp: new Date(),
            method: 'socket-registration'
          });
        } else {
          console.log(`⚠️ Failed to update client status via state manager: ${clientId}`);
        }
      } else {
        // Fallback to direct database update
        console.log('⚠️ ClientStateManager not available, using fallback');

        const { getDatabase } = require('./database/init');
        const db = getDatabase();

        const updateResult = await db.query(`
          UPDATE desktop_clients SET
            status = 'online',
            last_seen = CURRENT_TIMESTAMP
          WHERE client_id = ?
        `, [clientId]);

        console.log(`📊 Socket registration - Database update result: ${updateResult.affectedRows || updateResult.rowCount || 'unknown'} rows affected`);
      }

      // Notify web dashboard about client connection
      socket.to(`user-${userId}`).emit('client-connected', {
        clientId,
        status: 'online',
        timestamp: new Date()
      });

      console.log(`✅ Client ${clientId} socket registered and joined rooms`);

    } catch (error) {
      console.error('❌ Client registration error:', error);
    }
  });

  // Handle client status updates
  socket.on('client-status', async (status) => {
    try {
      console.log(`📊 Client status update: ${status.clientId}`);

      // Update database
      const { getDatabase } = require('./database/init');
      const db = getDatabase();

      await db.query(`
        UPDATE desktop_clients SET
          status = 'online',
          last_seen = CURRENT_TIMESTAMP,
          metadata = ?
        WHERE client_id = ?
      `, [JSON.stringify(status.systemInfo || {}), status.clientId]);

      // Broadcast to web dashboard
      socket.to(`user-${status.userId}`).emit('client-status-update', status);

    } catch (error) {
      console.error('❌ Client status update error:', error);
    }
  });

  // Handle client events (sync events, etc.)
  socket.on('client-event', async (event) => {
    try {
      console.log(`📨 Client event: ${event.eventType} from ${event.clientId}`);

      // Broadcast to web dashboard
      socket.to(`user-${event.userId}`).emit('client-event', event);

    } catch (error) {
      console.error('❌ Client event handling error:', error);
    }
  });

  // Handle sync events
  socket.on('sync-start', (data) => {
    socket.to(`user-${data.userId}`).emit('sync-started', data);
  });

  socket.on('sync-progress', (data) => {
    socket.to(`user-${data.userId}`).emit('sync-progress', data);
  });

  socket.on('sync-complete', (data) => {
    socket.to(`user-${data.userId}`).emit('sync-completed', data);
  });

  socket.on('sync-error', (data) => {
    socket.to(`user-${data.userId}`).emit('sync-error', data);
  });

  // Handle client disconnect event (explicit disconnect)
  socket.on('client-disconnect', async (data) => {
    try {
      const { clientId, userId } = data;
      console.log(`🚪 Client explicitly disconnecting: ${clientId} for user ${userId}`);

      // Update client status using state manager
      const clientStateManager = socket.request.app?.get('clientStateManager');

      if (clientStateManager) {
        const updateSuccess = await clientStateManager.updateClientStatus(clientId, 'offline', {
          disconnectReason: 'explicit_logout',
          lastExplicitDisconnect: new Date()
        });

        if (updateSuccess) {
          console.log(`✅ Client marked offline via state manager: ${clientId}`);
        } else {
          console.log(`⚠️ Failed to mark client offline via state manager: ${clientId}`);
        }
      } else {
        // Fallback to direct database update
        console.log('⚠️ ClientStateManager not available, using fallback');

        const { getDatabase } = require('./database/init');
        const db = getDatabase();

        const result = await db.query(`
          UPDATE desktop_clients SET
            status = 'offline',
            last_seen = CURRENT_TIMESTAMP
          WHERE client_id = ?
        `, [clientId]);

        console.log(`📊 Database update result: ${result.affectedRows || result.rowCount || 'unknown'} rows affected`);
      }

      // Notify ALL users in the user room about client disconnection
      io.to(`user-${userId}`).emit('client-disconnected', {
        clientId,
        status: 'offline',
        timestamp: new Date(),
        reason: 'explicit_logout'
      });

      console.log(`📴 Client ${clientId} marked as offline and notification sent to user-${userId} room`);

    } catch (error) {
      console.error('❌ Client disconnect handling error:', error);
    }
  });

  // Handle socket disconnect (network/unexpected disconnect)
  socket.on('disconnect', async () => {
    console.log('🔌 Socket disconnected:', socket.id);

    // Try to find and update any clients associated with this socket
    try {
      const { getDatabase } = require('./database/init');
      const db = getDatabase();

      // Get client info from socket handshake if available
      const clientId = socket.handshake.auth?.clientId;
      const userId = socket.handshake.auth?.userId;

      if (clientId) {
        console.log(`📴 Marking client ${clientId} as offline due to socket disconnect`);

        await db.query(`
          UPDATE desktop_clients SET
            status = 'offline',
            last_seen = CURRENT_TIMESTAMP
          WHERE client_id = ?
        `, [clientId]);

        // Notify web dashboard
        if (userId) {
          socket.to(`user-${userId}`).emit('client-disconnected', {
            clientId,
            status: 'offline',
            reason: 'socket_disconnect',
            timestamp: new Date()
          });
        }

        console.log(`✅ Client ${clientId} marked as offline`);
      }

    } catch (error) {
      console.error('❌ Error handling socket disconnect:', error);
    }
  });
});

// Auto-resume sync tasks on server startup
async function autoResumeSyncTasks(realtimeSyncEngine) {
  try {
    console.log('🔄 Checking for tasks to auto-resume...');
    const { getDatabase } = require('./database/init');
    const db = getDatabase();

    // Find tasks that were running when server stopped
    const runningTasks = await db.query(
      `SELECT * FROM sync_tasks
       WHERE status IN ('running', 'monitoring')
       ORDER BY updated_at DESC`
    );

    if (runningTasks.rows.length === 0) {
      console.log('✅ No tasks to resume');
      return;
    }

    console.log(`🔄 Found ${runningTasks.rows.length} tasks to resume`);

    for (const task of runningTasks.rows) {
      try {
        // Parse options
        const options = typeof task.options === 'string' ? JSON.parse(task.options) : task.options;

        // Map database field names
        const mappedTask = {
          ...task,
          sourcePath: task.source_path,
          destinationPath: task.destination_path,
          syncType: task.sync_type,
          options
        };

        if (options?.enableRealtime && task.status === 'monitoring') {
          // Resume real-time sync
          console.log(`⚡ Resuming real-time sync: ${task.name}`);
          await realtimeSyncEngine.startRealtimeSync(mappedTask);

          // Update status to monitoring
          await db.query(
            'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            ['monitoring', task.id]
          );

        } else if (task.status === 'running') {
          // Reset running tasks to idle (they were interrupted)
          console.log(`🔄 Resetting interrupted task: ${task.name}`);
          await db.query(
            'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            ['idle', task.id]
          );
        }

      } catch (error) {
        console.error(`❌ Failed to resume task ${task.name}:`, error);

        // Set task to error state
        await db.query(
          'UPDATE sync_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          ['error', task.id]
        );
      }
    }

    console.log('✅ Task auto-resume completed');

  } catch (error) {
    console.error('❌ Failed to auto-resume tasks:', error);
  }
}

// Initialize database and start server
const PORT = process.env.PORT || 5000;

async function startServer() {
  try {
    // Initialize database
    await initializeDatabase();
    console.log('Database initialized successfully');

    // Import and setup routes after database initialization
    const authRoutes = require('./routes/auth');
    const { router: syncRoutes, initSyncEngine } = require('./routes/sync');
    const userRoutes = require('./routes/users');

    // Add test route first
    app.get('/api/test', (req, res) => {
      res.json({ message: 'API is working', timestamp: new Date().toISOString() });
    });

    // Setup routes
    app.use('/api/auth', authRoutes);
    app.use('/api/sync', authMiddleware, syncRoutes);
    app.use('/api/users', authMiddleware, userRoutes);
    app.use('/api/versions', authMiddleware, require('./routes/versions'));
    app.use('/api/conflicts', authMiddleware, require('./routes/conflicts'));
    app.use('/api/profiles', authMiddleware, require('./routes/profiles'));
    app.use('/api/filters', authMiddleware, require('./routes/filters'));
    app.use('/api/cloud', authMiddleware, require('./routes/cloud'));
    app.use('/api/analytics', authMiddleware, require('./routes/analytics'));
    app.use('/api/audit', authMiddleware, require('./routes/audit'));
    app.use('/api/admin', authMiddleware, require('./routes/admin'));
    app.use('/api/clients', require('./routes/clients'));
    app.use('/api/database-sync', require('./routes/database-sync'));
    console.log('Routes initialized successfully');

    // List all registered routes for debugging
    console.log('📋 Registered routes:');
    app._router.stack.forEach((middleware) => {
      if (middleware.route) {
        console.log(`  ${Object.keys(middleware.route.methods).join(', ').toUpperCase()} ${middleware.route.path}`);
      } else if (middleware.name === 'router') {
        middleware.handle.stack.forEach((handler) => {
          if (handler.route) {
            console.log(`  ${Object.keys(handler.route.methods).join(', ').toUpperCase()} ${middleware.regexp.source.replace('\\/?', '').replace('\\', '')}${handler.route.path}`);
          }
        });
      }
    });

    // Initialize sync engines with Socket.IO
    const { syncEngine, realtimeSyncEngine, extendedSyncEngine } = initSyncEngine(io);
    app.set('syncEngine', syncEngine);
    app.set('realtimeSyncEngine', realtimeSyncEngine);
    app.set('extendedSyncEngine', extendedSyncEngine);
    console.log('Sync engines initialized with Socket.IO');

    // Add 404 handler after all routes are setup
    app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: 'The requested resource was not found'
      });
    });

    // Initialize Client Manager for desktop mode
    if (process.env.ELECTRON_ENV === 'true' && extendedSyncEngine) {
      console.log('🖥️ Desktop mode detected, Client Manager will be initialized after user login');
    }

    // Initialize enhanced database sync system
    const DatabaseSyncService = require('./services/DatabaseSyncService');
    const RealTimeDatabaseSync = require('./services/RealTimeDatabaseSync');
    const DatabaseChangeListener = require('./services/DatabaseChangeListener');
    const ClientStateManager = require('./services/ClientStateManager');

    const dbSyncService = new DatabaseSyncService();
    const realTimeSync = new RealTimeDatabaseSync(dbSyncService);
    const changeListener = new DatabaseChangeListener();
    const clientStateManager = new ClientStateManager();

    try {
      await dbSyncService.initialize({
        enableAutoSync: process.env.ENABLE_DB_SYNC === 'true',
        syncIntervalMinutes: parseInt(process.env.DB_SYNC_INTERVAL) || 15,
        syncDirection: process.env.DB_SYNC_DIRECTION || 'bidirectional'
      });

      // Initialize client state manager
      await clientStateManager.initialize(db);

      // Initialize change listener with real-time sync
      changeListener.initialize(dbSyncService, realTimeSync);

      // Store services in app for access by other modules
      app.set('dbSyncService', dbSyncService);
      app.set('realTimeDatabaseSync', realTimeSync);
      app.set('changeListener', changeListener);
      app.set('clientStateManager', clientStateManager);

      // Start services if enabled
      if (process.env.ENABLE_DB_SYNC === 'true') {
        // Enable real-time sync
        realTimeSync.enable();

        // Start listening for changes
        changeListener.startListening();

        // Start scheduled sync
        dbSyncService.startAutoSync();

        console.log('🎉 Enhanced database sync system started');
      }

      console.log('✅ Database sync service initialized');
    } catch (error) {
      console.log('⚠️ Database sync service initialization failed:', error.message);
      console.log('💡 Continuing without database sync (PostgreSQL may not be available)');
    }

    // Start server
    server.listen(PORT, async () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`Database: ${process.env.DB_TYPE || 'sqlite'}`);

      // Auto-resume sync tasks after server startup
      setTimeout(() => autoResumeSyncTasks(realtimeSyncEngine), 2000);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown with sync task cleanup
async function gracefulShutdown(signal) {
  console.log(`${signal} received, shutting down gracefully`);

  try {
    // Get sync engines
    const syncEngine = app.get('syncEngine');
    const realtimeSyncEngine = app.get('realtimeSyncEngine');

    // Stop all active syncs
    if (syncEngine && syncEngine.getActiveSyncs) {
      const activeSyncs = syncEngine.getActiveSyncs();
      if (activeSyncs && Array.isArray(activeSyncs)) {
        console.log(`🛑 Stopping ${activeSyncs.length} active syncs...`);

        for (const taskId of activeSyncs) {
          try {
            await syncEngine.stopSync(taskId);
          } catch (error) {
            console.error(`Failed to stop sync ${taskId}:`, error);
          }
        }
      } else {
        console.log(`🛑 Stopping ${activeSyncs ? 'unknown' : '0'} active syncs...`);
      }
    }

    // Stop all real-time syncs
    if (realtimeSyncEngine && realtimeSyncEngine.stopAllRealtimeSync) {
      console.log('⚡ Stopping all real-time syncs...');
      await realtimeSyncEngine.stopAllRealtimeSync();
    }

    console.log('✅ All sync tasks stopped');

  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
  }

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

startServer();

module.exports = { app, io };
