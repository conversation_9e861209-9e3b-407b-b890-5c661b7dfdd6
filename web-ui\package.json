{"name": "syncmasterpro-web-ui", "version": "1.0.0", "description": "SyncMasterPro Web Management Interface", "private": true, "dependencies": {"@heroicons/react": "^2.0.18", "axios": "^1.6.0", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.1"}, "scripts": {"start": "cross-env PORT=3001 REACT_APP_API_URL=http://localhost:5001/api BROWSER=none react-scripts start", "build": "cross-env REACT_APP_API_URL=/api react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"cross-env": "^7.0.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "overrides": {"nth-check": ">=2.0.1"}}