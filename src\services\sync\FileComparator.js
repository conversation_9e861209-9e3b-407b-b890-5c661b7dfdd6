/**
 * FileComparator Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles file comparison logic
 * - Pure Functions: No side effects, predictable outputs
 * - Clear Naming: Method names clearly indicate their purpose
 * - DRY: Reusable comparison logic
 */
class FileComparator {

  /**
   * Compares two file arrays and determines what changes need to be made
   * @param {Array} sourceFiles - Files from source directory
   * @param {Array} destFiles - Files from destination directory
   * @param {Object} options - Comparison options
   * @returns {Object} Comparison results with files to add, update, delete
   */
  static compareFiles(sourceFiles, destFiles, options = {}) {
    const {
      compareByChecksum = true,
      compareBySize = true,
      compareByModTime = true,
      toleranceMs = 1000 // 1 second tolerance for modification time
    } = options;

    // Create maps for efficient lookup
    const sourceMap = this._createFileMap(sourceFiles);
    const destMap = this._createFileMap(destFiles);

    const changes = {
      toAdd: [],      // Files that exist in source but not in destination
      toUpdate: [],   // Files that exist in both but are different
      toDelete: [],   // Files that exist in destination but not in source
      identical: []   // Files that are identical in both locations
    };

    // Check source files against destination
    for (const sourceFile of sourceFiles) {
      const destFile = destMap.get(sourceFile.relativePath);
      
      if (!destFile) {
        changes.toAdd.push(sourceFile);
      } else {
        const comparisonResult = this._compareFileDetails(
          sourceFile, 
          destFile, 
          { compareByChecksum, compareBySize, compareByModTime, toleranceMs }
        );
        
        if (comparisonResult.needsUpdate) {
          changes.toUpdate.push({
            source: sourceFile,
            dest: destFile,
            reason: comparisonResult.reason
          });
        } else {
          changes.identical.push({
            source: sourceFile,
            dest: destFile
          });
        }
      }
    }

    // Check for files that exist only in destination
    for (const destFile of destFiles) {
      if (!sourceMap.has(destFile.relativePath)) {
        changes.toDelete.push(destFile);
      }
    }

    return changes;
  }

  /**
   * Compares two individual files to determine if update is needed
   * @param {Object} sourceFile - Source file information
   * @param {Object} destFile - Destination file information
   * @param {Object} options - Comparison options
   * @returns {Object} Comparison result
   */
  static _compareFileDetails(sourceFile, destFile, options) {
    const reasons = [];

    // Compare by checksum (most reliable)
    if (options.compareByChecksum && sourceFile.checksum && destFile.checksum) {
      if (sourceFile.checksum !== destFile.checksum) {
        reasons.push('checksum_different');
      }
    }

    // Compare by file size
    if (options.compareBySize) {
      if (sourceFile.size !== destFile.size) {
        reasons.push('size_different');
      }
    }

    // Compare by modification time
    if (options.compareByModTime) {
      const sourceTime = new Date(sourceFile.mtime).getTime();
      const destTime = new Date(destFile.mtime).getTime();
      const timeDiff = Math.abs(sourceTime - destTime);
      
      if (timeDiff > options.toleranceMs) {
        if (sourceTime > destTime) {
          reasons.push('source_newer');
        } else {
          reasons.push('dest_newer');
        }
      }
    }

    return {
      needsUpdate: reasons.length > 0,
      reason: reasons.join(', '),
      reasons
    };
  }

  /**
   * Creates a map of files indexed by relative path for efficient lookup
   * @param {Array} files - Array of file objects
   * @returns {Map} Map of files indexed by relative path
   */
  static _createFileMap(files) {
    const map = new Map();
    for (const file of files) {
      map.set(file.relativePath, file);
    }
    return map;
  }

  /**
   * Determines sync direction for bidirectional sync
   * @param {Object} sourceFile - Source file information
   * @param {Object} destFile - Destination file information
   * @returns {string} Sync direction: 'source-to-dest', 'dest-to-source', or 'conflict'
   */
  static determineSyncDirection(sourceFile, destFile) {
    if (!sourceFile) return 'dest-to-source';
    if (!destFile) return 'source-to-dest';

    const sourceTime = new Date(sourceFile.mtime).getTime();
    const destTime = new Date(destFile.mtime).getTime();
    const timeDiff = sourceTime - destTime;

    // If times are very close (within 1 second), consider them equal
    if (Math.abs(timeDiff) <= 1000) {
      // Use checksum to determine if files are different
      if (sourceFile.checksum !== destFile.checksum) {
        return 'conflict'; // Same time but different content
      }
      return 'identical';
    }

    return timeDiff > 0 ? 'source-to-dest' : 'dest-to-source';
  }

  /**
   * Filters files based on sync type and date criteria
   * @param {Array} files - Array of file objects
   * @param {string} syncType - Type of sync operation
   * @param {Object} options - Additional filtering options
   * @returns {Array} Filtered files
   */
  static filterFilesBySyncType(files, syncType, options = {}) {
    switch (syncType) {
      case 'today-only':
        return this._filterTodayFiles(files);
      
      case 'incremental':
        return this._filterIncrementalFiles(files, options.lastSyncTime);
      
      case 'size-based':
        return this._filterBySizeRange(files, options.minSize, options.maxSize);
      
      default:
        return files;
    }
  }

  /**
   * Filters files modified today
   * @param {Array} files - Array of file objects
   * @returns {Array} Files modified today
   */
  static _filterTodayFiles(files) {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    return files.filter(file => {
      const fileTime = new Date(file.mtime);
      return fileTime >= startOfDay && fileTime < endOfDay;
    });
  }

  /**
   * Filters files for incremental sync (modified since last sync)
   * @param {Array} files - Array of file objects
   * @param {Date} lastSyncTime - Time of last sync
   * @returns {Array} Files modified since last sync
   */
  static _filterIncrementalFiles(files, lastSyncTime) {
    if (!lastSyncTime) return files;

    const syncTime = new Date(lastSyncTime);
    return files.filter(file => {
      const fileTime = new Date(file.mtime);
      return fileTime > syncTime;
    });
  }

  /**
   * Filters files by size range
   * @param {Array} files - Array of file objects
   * @param {number} minSize - Minimum file size in bytes
   * @param {number} maxSize - Maximum file size in bytes
   * @returns {Array} Files within size range
   */
  static _filterBySizeRange(files, minSize = 0, maxSize = Infinity) {
    return files.filter(file => 
      file.size >= minSize && file.size <= maxSize
    );
  }

  /**
   * Analyzes sync changes and provides statistics
   * @param {Object} changes - Changes object from compareFiles
   * @returns {Object} Analysis statistics
   */
  static analyzeChanges(changes) {
    const totalFiles = changes.toAdd.length + changes.toUpdate.length + 
                      changes.toDelete.length + changes.identical.length;
    
    const totalSizeToTransfer = [
      ...changes.toAdd,
      ...changes.toUpdate.map(u => u.source)
    ].reduce((sum, file) => sum + file.size, 0);

    return {
      totalFiles,
      filesToAdd: changes.toAdd.length,
      filesToUpdate: changes.toUpdate.length,
      filesToDelete: changes.toDelete.length,
      identicalFiles: changes.identical.length,
      totalSizeToTransfer,
      changePercentage: totalFiles > 0 ? 
        Math.round(((changes.toAdd.length + changes.toUpdate.length + changes.toDelete.length) / totalFiles) * 100) : 0
    };
  }

  /**
   * Validates comparison results for potential issues
   * @param {Object} changes - Changes object from compareFiles
   * @returns {Array} Array of validation warnings
   */
  static validateChanges(changes) {
    const warnings = [];

    // Check for potential data loss
    if (changes.toDelete.length > changes.toAdd.length * 2) {
      warnings.push('Large number of deletions detected - potential data loss risk');
    }

    // Check for unusual update patterns
    if (changes.toUpdate.length > changes.identical.length && changes.toAdd.length === 0) {
      warnings.push('All existing files need updates - verify source integrity');
    }

    // Check for empty sync
    if (changes.toAdd.length === 0 && changes.toUpdate.length === 0 && changes.toDelete.length === 0) {
      warnings.push('No changes detected - directories may already be synchronized');
    }

    return warnings;
  }
}

module.exports = FileComparator;
