import React from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useSyncTasksManager } from '../../hooks/useSyncTasksManager';
import TaskCard from './TaskCard';
import CreateTaskModal from './CreateTaskModal';
import TasksHeader from './TasksHeader';
import TasksStats from './TasksStats';
import TasksFilters from './TasksFilters';
import TasksList from './TasksList';
import { getFilterDisplayTitle } from '../../utils/taskFilters';

/**
 * SyncTasks Component - Refactored for Clean Code
 *
 * Improvements made:
 * - Single Responsibility: Only orchestrates child components
 * - Separation of Concerns: Business logic moved to custom hook
 * - DRY: Reusable components extracted
 * - Readable: Clear component structure and naming
 */
const SyncTasks = () => {
  const { isConnected } = useSocket();
  const { t } = useLanguage();

  // Custom hook handles all business logic
  const {
    syncTasks,
    filteredTasks,
    activeSyncs,
    stats,
    filterStatus,
    searchTerm,
    showCreateModal,
    showEditModal,
    editingTask,
    filterOptions,
    handleCreateTask,
    handleEditTask,
    openCreateModal,
    closeCreateModal,
    openEditModal,
    closeEditModal,
    startSync,
    stopSync,
    deleteSyncTask,
    setFilterStatus,
    setSearchTerm,
    clearFilters
  } = useSyncTasksManager();

  return (
    <div className="space-y-6">
      <TasksHeader
        title={t('syncTasksTitle')}
        description={t('syncTasksDescription')}
        isConnected={isConnected}
        connectionStatusText={isConnected ? t('realTimeConnected') : t('offlineMode')}
        onCreateTask={openCreateModal}
        createButtonText={t('createNewTask')}
      />

      <TasksStats
        stats={stats}
        translations={t}
      />

      <TasksFilters
        filterStatus={filterStatus}
        searchTerm={searchTerm}
        onFilterChange={setFilterStatus}
        onSearchChange={setSearchTerm}
        filterOptions={filterOptions}
        translations={t}
        totalTasks={syncTasks.length}
      />

      <TasksList
        tasks={filteredTasks}
        allTasks={syncTasks}
        activeSyncs={activeSyncs}
        filterStatus={filterStatus}
        onCreateTask={openCreateModal}
        onEditTask={openEditModal}
        onStartSync={startSync}
        onStopSync={stopSync}
        onDeleteTask={deleteSyncTask}
        onClearFilters={clearFilters}
        translations={t}
      />

      {/* Modals */}
      {showCreateModal && (
        <CreateTaskModal
          onClose={closeCreateModal}
          onCreate={handleCreateTask}
        />
      )}

      {showEditModal && editingTask && (
        <CreateTaskModal
          onClose={closeEditModal}
          onCreate={handleEditTask}
          initialData={editingTask}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default SyncTasks;





export default SyncTasks;
