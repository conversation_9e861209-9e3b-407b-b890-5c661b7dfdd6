import React from 'react';
import { MagnifyingGlassIcon } from '../UI/Icons';

/**
 * TasksFilters Component
 * 
 * Clean Code principles applied:
 * - Single Responsibility: Only handles filtering UI
 * - Clear naming: Self-documenting props and functions
 * - Separation of concerns: No business logic, only presentation
 */
const TasksFilters = ({
  filterStatus,
  searchTerm,
  onFilterChange,
  onSearchChange,
  filterOptions,
  translations,
  totalTasks
}) => {
  if (totalTasks === 0) {
    return null; // Don't render filters if no tasks exist
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        
        <FilterDropdown
          value={filterStatus}
          onChange={onFilterChange}
          options={filterOptions}
          label={`${translations.filter} by ${translations.status}`}
        />

        <SearchInput
          value={searchTerm}
          onChange={onSearchChange}
          placeholder="Search by name, source, or destination..."
          label={`${translations.search} Tasks`}
        />
      </div>
    </div>
  );
};

/**
 * FilterDropdown Component
 * 
 * Single responsibility: Render filter dropdown
 */
const FilterDropdown = ({ value, onChange, options, label }) => (
  <div>
    <label 
      htmlFor="filter" 
      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
    >
      {label}
    </label>
    <select
      id="filter"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white transition-colors duration-200"
    >
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  </div>
);

/**
 * SearchInput Component
 * 
 * Single responsibility: Render search input with icon
 */
const SearchInput = ({ value, onChange, placeholder, label }) => (
  <div className="flex-1 max-w-md">
    <label 
      htmlFor="search" 
      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
    >
      {label}
    </label>
    <div className="relative">
      <input
        type="text"
        id="search"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="block w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 transition-colors duration-200"
      />
      <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
    </div>
  </div>
);

export default TasksFilters;
