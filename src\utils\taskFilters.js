/**
 * Task Filtering Utilities
 * 
 * Clean Code principles:
 * - Pure functions: No side effects
 * - Single responsibility: Each function has one clear purpose
 * - Descriptive naming: Function names clearly indicate their purpose
 * - DRY: Reusable filtering logic
 */

/**
 * Filters tasks based on status
 * @param {Array} tasks - Array of task objects
 * @param {string} filterStatus - Status to filter by
 * @param {Set} activeSyncs - Set of active sync IDs
 * @returns {Array} Filtered tasks
 */
export const filterTasksByStatus = (tasks, filterStatus, activeSyncs) => {
  if (filterStatus === 'all') {
    return tasks;
  }

  return tasks.filter(task => {
    switch (filterStatus) {
      case 'active':
        return activeSyncs.has(task.id);
      case 'idle':
        return !activeSyncs.has(task.id) && task.status !== 'error';
      case 'error':
        return task.status === 'error';
      case 'completed':
        return task.status === 'completed';
      default:
        return true;
    }
  });
};

/**
 * Filters tasks based on search term
 * @param {Array} tasks - Array of task objects
 * @param {string} searchTerm - Search term to filter by
 * @returns {Array} Filtered tasks
 */
export const filterTasksBySearch = (tasks, searchTerm) => {
  if (!searchTerm.trim()) {
    return tasks;
  }

  const normalizedSearchTerm = searchTerm.toLowerCase();
  
  return tasks.filter(task => 
    isTaskMatchingSearch(task, normalizedSearchTerm)
  );
};

/**
 * Checks if a task matches the search term
 * @param {Object} task - Task object
 * @param {string} searchTerm - Normalized search term
 * @returns {boolean} Whether task matches search
 */
const isTaskMatchingSearch = (task, searchTerm) => {
  const searchableFields = [
    task.name,
    task.sourcePath || task.source_path,
    task.destinationPath || task.destination_path,
    task.description
  ].filter(Boolean); // Remove undefined/null values

  return searchableFields.some(field => 
    field.toLowerCase().includes(searchTerm)
  );
};

/**
 * Combines status and search filtering
 * @param {Array} tasks - Array of task objects
 * @param {string} filterStatus - Status to filter by
 * @param {string} searchTerm - Search term to filter by
 * @param {Set} activeSyncs - Set of active sync IDs
 * @returns {Array} Filtered tasks
 */
export const filterTasks = (tasks, filterStatus, searchTerm, activeSyncs) => {
  let filteredTasks = filterTasksByStatus(tasks, filterStatus, activeSyncs);
  filteredTasks = filterTasksBySearch(filteredTasks, searchTerm);
  return filteredTasks;
};

/**
 * Calculates task statistics
 * @param {Array} tasks - Array of task objects
 * @param {Set} activeSyncs - Set of active sync IDs
 * @returns {Object} Task statistics
 */
export const calculateTaskStats = (tasks, activeSyncs) => {
  return {
    totalTasks: tasks.length,
    activeTasks: activeSyncs.size,
    scheduledTasks: tasks.filter(task => task.schedule).length,
    errorTasks: tasks.filter(task => task.status === 'error').length,
    idleTasks: tasks.filter(task => 
      !activeSyncs.has(task.id) && task.status !== 'error'
    ).length,
    completedTasks: tasks.filter(task => task.status === 'completed').length
  };
};

/**
 * Generates filter options for dropdown
 * @param {Object} stats - Task statistics
 * @param {Object} translations - Translation object
 * @returns {Array} Filter options
 */
export const generateFilterOptions = (stats, translations) => [
  {
    value: 'all',
    label: `All Tasks (${stats.totalTasks})`
  },
  {
    value: 'active',
    label: `${translations.activeSyncs} (${stats.activeTasks})`
  },
  {
    value: 'idle',
    label: `Idle (${stats.idleTasks})`
  },
  {
    value: 'completed',
    label: `${translations.completed} (${stats.completedTasks})`
  },
  {
    value: 'error',
    label: `Error (${stats.errorTasks})`
  }
];

/**
 * Gets the display title for current filter
 * @param {string} filterStatus - Current filter status
 * @param {Object} translations - Translation object
 * @returns {string} Display title
 */
export const getFilterDisplayTitle = (filterStatus, translations) => {
  const titleMap = {
    all: translations.allSyncTasks,
    active: translations.activeTasks,
    completed: translations.completedTasks,
    error: translations.errorTasks,
    idle: 'Idle Tasks'
  };

  return titleMap[filterStatus] || 
    `${filterStatus.charAt(0).toUpperCase() + filterStatus.slice(1)} Tasks`;
};
