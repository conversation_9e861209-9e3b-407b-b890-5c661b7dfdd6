import React from 'react';
import { 
  FolderIcon, 
  PlayIcon, 
  ClockIcon, 
  ExclamationTriangleIcon 
} from '../UI/Icons';

/**
 * TasksStats Component
 * 
 * Follows Clean Code principles:
 * - Single Responsibility: Only displays statistics
 * - DRY: Uses reusable StatCard component
 * - Clear naming: Self-documenting function and variable names
 */
const TasksStats = ({ stats, translations }) => {
  const statsConfig = [
    {
      title: translations.totalSyncTasks,
      value: stats.totalTasks,
      icon: FolderIcon,
      color: 'blue',
      bgColor: 'bg-blue-50 dark:bg-blue-900',
      textColor: 'text-blue-600 dark:text-blue-400'
    },
    {
      title: translations.activeSyncs,
      value: stats.activeTasks,
      icon: PlayIcon,
      color: 'green',
      bgColor: 'bg-green-50 dark:bg-green-900',
      textColor: 'text-green-600 dark:text-green-400'
    },
    {
      title: 'Scheduled',
      value: stats.scheduledTasks,
      icon: ClockIcon,
      color: 'yellow',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900',
      textColor: 'text-yellow-600 dark:text-yellow-400'
    },
    {
      title: 'Errors',
      value: stats.errorTasks,
      icon: ExclamationTriangleIcon,
      color: 'red',
      bgColor: 'bg-red-50 dark:bg-red-900',
      textColor: 'text-red-600 dark:text-red-400'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {statsConfig.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

/**
 * StatCard Component
 * 
 * Reusable component following DRY principle
 * Single responsibility: Display a single statistic
 */
const StatCard = ({ 
  title, 
  value, 
  icon: Icon, 
  bgColor, 
  textColor 
}) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div className="flex items-center">
      <div className={`p-3 rounded-lg ${bgColor} ${textColor}`}>
        <Icon className="w-6 h-6" />
      </div>
      <div className="ml-4">
        <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
          {title}
        </p>
        <p className="text-2xl font-bold text-gray-900 dark:text-white">
          {value}
        </p>
      </div>
    </div>
  </div>
);

export default TasksStats;
