import React from 'react';
import { PlusIcon } from '../UI/Icons';

/**
 * TasksHeader Component
 * 
 * Follows Single Responsibility Principle:
 * - Only responsible for rendering the header section
 * - Receives all needed data as props
 * - No business logic, just presentation
 */
const TasksHeader = ({ 
  title, 
  description, 
  isConnected, 
  connectionStatusText, 
  onCreateTask,
  createButtonText 
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <div className="flex items-center space-x-3">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {title}
          </h1>
          
          {/* Real-time Connection Status Indicator */}
          <ConnectionStatus 
            isConnected={isConnected} 
            statusText={connectionStatusText} 
          />
        </div>
        
        <p className="text-gray-600 dark:text-gray-300 mt-1">
          {description}
        </p>
      </div>
      
      <CreateTaskButton 
        onClick={onCreateTask}
        text={createButtonText}
      />
    </div>
  );
};

/**
 * ConnectionStatus Component
 * 
 * Single responsibility: Display connection status
 */
const ConnectionStatus = ({ isConnected, statusText }) => (
  <div className="flex items-center space-x-2">
    <div className={`w-2 h-2 rounded-full ${
      isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'
    }`} />
    <span className="text-xs text-gray-500 dark:text-gray-400">
      {statusText}
    </span>
  </div>
);

/**
 * CreateTaskButton Component
 * 
 * Single responsibility: Render create task button
 */
const CreateTaskButton = ({ onClick, text }) => (
  <button
    onClick={onClick}
    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
    aria-label="Create new sync task"
  >
    <PlusIcon className="w-4 h-4 mr-2" />
    {text}
  </button>
);

export default TasksHeader;
