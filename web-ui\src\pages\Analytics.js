import React, { useState, useEffect , useCallback} from 'react';
import { useAuth } from '../contexts/AuthContext';

const Analytics = () => {
  const { token } = useAuth();
  const [analyticsData, setAnalyticsData] = useState({
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    totalDataTransferred: 0,
    averageSyncTime: 0
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (token) {
      loadAnalyticsData();
    }
  }, [token, loadAnalyticsData]);

  const loadAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load sync history for analytics
      const historyResponse = await fetch('http://localhost:5001/api/sync/history', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!historyResponse.ok) {
        throw new Error(`Failed to load sync history: ${historyResponse.status}`);
      }

      const historyData = await historyResponse.json();
      const history = historyData.history || [];

      // Calculate analytics from real data
      const totalSyncs = history.length;
      const successfulSyncs = history.filter(h => h.status === 'completed').length;
      const failedSyncs = history.filter(h => h.status === 'error').length;

      // Calculate total data transferred (sum of total_size)
      const totalBytes = history.reduce((sum, h) => sum + (h.total_size || 0), 0);
      const totalDataTransferred = totalBytes / (1024 * 1024 * 1024); // Convert to GB

      // Calculate average sync time
      const completedSyncs = history.filter(h => h.status === 'completed' && h.duration);
      const averageSyncTime = completedSyncs.length > 0
        ? completedSyncs.reduce((sum, h) => sum + (h.duration || 0), 0) / completedSyncs.length / 1000
        : 0;

      setAnalyticsData({
        totalSyncs,
        successfulSyncs,
        failedSyncs,
        totalDataTransferred,
        averageSyncTime
      });

      // Transform recent history for activity display
      const recentHistory = history.slice(0, 10).map(h => ({
        id: h.id,
        client: `Task #${h.task_id}`,
        action: getActionText(h.status),
        time: formatTimeAgo(h.created_at),
        status: mapStatus(h.status)
      }));

      setRecentActivity(recentHistory);
      console.log(`📊 Analytics loaded: ${totalSyncs} total syncs, ${successfulSyncs} successful`);

    } catch (error) {
      console.error('❌ Failed to load analytics:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [token]);

  const getActionText = (status) => {
    switch (status) {
      case 'completed': return 'Sync Completed';
      case 'running': return 'Sync Running';
      case 'error': return 'Sync Failed';
      case 'paused': return 'Sync Paused';
      default: return 'Sync Activity';
    }
  };

  const mapStatus = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': return 'running';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const formatTimeAgo = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const formatFileSize = (gb) => {
    return `${gb.toFixed(1)} GB`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'info': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600">Monitor sync performance and system statistics</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600">Monitor sync performance and system statistics</p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading analytics</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={loadAnalyticsData}
                className="mt-2 text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
        <p className="text-gray-600">Monitor sync performance and system statistics</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-3xl font-bold text-blue-600">{analyticsData.totalSyncs.toLocaleString()}</p>
            <p className="text-sm text-gray-600 mt-1">Total Syncs</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-3xl font-bold text-green-600">{analyticsData.successfulSyncs.toLocaleString()}</p>
            <p className="text-sm text-gray-600 mt-1">Successful</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-3xl font-bold text-red-600">{analyticsData.failedSyncs}</p>
            <p className="text-sm text-gray-600 mt-1">Failed</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-3xl font-bold text-purple-600">{formatFileSize(analyticsData.totalDataTransferred)}</p>
            <p className="text-sm text-gray-600 mt-1">Data Transferred</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-3xl font-bold text-orange-600">{analyticsData.averageSyncTime}s</p>
            <p className="text-sm text-gray-600 mt-1">Avg Sync Time</p>
          </div>
        </div>
      </div>

      {/* Success Rate Chart */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Success Rate</h2>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div 
                className="bg-green-500 h-4 rounded-full" 
                style={{ width: `${(analyticsData.successfulSyncs / analyticsData.totalSyncs) * 100}%` }}
              ></div>
            </div>
          </div>
          <div className="text-right">
            <p className="text-2xl font-bold text-green-600">
              {((analyticsData.successfulSyncs / analyticsData.totalSyncs) * 100).toFixed(1)}%
            </p>
            <p className="text-sm text-gray-600">Success Rate</p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="mr-4">
                    <p className="font-medium text-gray-900">{activity.client}</p>
                    <p className="text-sm text-gray-500">{activity.action}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                    {activity.status}
                  </span>
                  <span className="text-sm text-gray-500">{activity.time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
