const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

/**
 * FileOperations Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles all file system operations
 * - DRY: Centralizes file operation logic
 * - Error Handling: Consistent error handling patterns
 * - Pure Functions: Most methods are stateless
 */
class FileOperations {
  
  /**
   * Scans a directory and returns file information
   * @param {string} dirPath - Directory path to scan
   * @param {Object} filters - File filters to apply
   * @returns {Promise<Array>} Array of file objects
   */
  static async scanDirectory(dirPath, filters = {}) {
    try {
      const files = [];
      await this._scanDirectoryRecursive(dirPath, dirPath, files, filters);
      return files;
    } catch (error) {
      throw new Error(`Failed to scan directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Recursively scans directory (private helper)
   */
  static async _scanDirectoryRecursive(basePath, currentPath, files, filters) {
    const items = await fs.readdir(currentPath, { withFileTypes: true });

    for (const item of items) {
      const fullPath = path.join(currentPath, item.name);
      
      if (item.isDirectory()) {
        if (this._shouldIncludeDirectory(item.name, filters)) {
          await this._scanDirectoryRecursive(basePath, fullPath, files, filters);
        }
      } else if (item.isFile()) {
        if (this._shouldIncludeFile(item.name, filters)) {
          const fileInfo = await this._getFileInfo(fullPath, basePath);
          files.push(fileInfo);
        }
      }
    }
  }

  /**
   * Gets detailed file information
   * @param {string} filePath - Path to the file
   * @param {string} basePath - Base path for relative path calculation
   * @returns {Promise<Object>} File information object
   */
  static async _getFileInfo(filePath, basePath) {
    const stats = await fs.stat(filePath);
    const relativePath = path.relative(basePath, filePath);
    
    return {
      path: filePath,
      relativePath,
      name: path.basename(filePath),
      size: stats.size,
      mtime: stats.mtime,
      ctime: stats.ctime,
      isDirectory: stats.isDirectory(),
      checksum: await this.calculateChecksum(filePath)
    };
  }

  /**
   * Calculates file checksum for comparison
   * @param {string} filePath - Path to the file
   * @returns {Promise<string>} File checksum
   */
  static async calculateChecksum(filePath) {
    try {
      const hash = crypto.createHash('md5');
      const stream = fs.createReadStream(filePath);
      
      return new Promise((resolve, reject) => {
        stream.on('data', data => hash.update(data));
        stream.on('end', () => resolve(hash.digest('hex')));
        stream.on('error', reject);
      });
    } catch (error) {
      // Return file size as fallback if checksum calculation fails
      const stats = await fs.stat(filePath);
      return `size-${stats.size}-${stats.mtime.getTime()}`;
    }
  }

  /**
   * Copies a file from source to destination
   * @param {string} sourcePath - Source file path
   * @param {string} destPath - Destination file path
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<boolean>} Success status
   */
  static async copyFile(sourcePath, destPath, progressCallback = null) {
    try {
      // Ensure destination directory exists
      await fs.ensureDir(path.dirname(destPath));
      
      // Copy file with progress tracking if callback provided
      if (progressCallback) {
        await this._copyFileWithProgress(sourcePath, destPath, progressCallback);
      } else {
        await fs.copy(sourcePath, destPath, { overwrite: true });
      }
      
      return true;
    } catch (error) {
      throw new Error(`Failed to copy file ${sourcePath} to ${destPath}: ${error.message}`);
    }
  }

  /**
   * Copies file with progress tracking (private helper)
   */
  static async _copyFileWithProgress(sourcePath, destPath, progressCallback) {
    const stats = await fs.stat(sourcePath);
    const totalSize = stats.size;
    let copiedSize = 0;

    const readStream = fs.createReadStream(sourcePath);
    const writeStream = fs.createWriteStream(destPath);

    return new Promise((resolve, reject) => {
      readStream.on('data', (chunk) => {
        copiedSize += chunk.length;
        progressCallback(copiedSize, totalSize);
      });

      readStream.on('error', reject);
      writeStream.on('error', reject);
      writeStream.on('finish', resolve);

      readStream.pipe(writeStream);
    });
  }

  /**
   * Deletes a file or directory
   * @param {string} filePath - Path to delete
   * @returns {Promise<boolean>} Success status
   */
  static async deleteFile(filePath) {
    try {
      await fs.remove(filePath);
      return true;
    } catch (error) {
      throw new Error(`Failed to delete ${filePath}: ${error.message}`);
    }
  }

  /**
   * Checks if a file should be included based on filters
   * @param {string} fileName - Name of the file
   * @param {Object} filters - Filter configuration
   * @returns {boolean} Whether file should be included
   */
  static _shouldIncludeFile(fileName, filters) {
    if (!filters) return true;

    // Check file extensions
    if (filters.extensions && filters.extensions.length > 0) {
      const ext = path.extname(fileName).toLowerCase();
      const hasValidExtension = filters.extensions.some(allowedExt => 
        ext === allowedExt.toLowerCase()
      );
      if (!hasValidExtension) return false;
    }

    // Check excluded patterns
    if (filters.excludePatterns && filters.excludePatterns.length > 0) {
      const isExcluded = filters.excludePatterns.some(pattern => 
        this._matchesPattern(fileName, pattern)
      );
      if (isExcluded) return false;
    }

    // Check included patterns
    if (filters.includePatterns && filters.includePatterns.length > 0) {
      const isIncluded = filters.includePatterns.some(pattern => 
        this._matchesPattern(fileName, pattern)
      );
      if (!isIncluded) return false;
    }

    return true;
  }

  /**
   * Checks if a directory should be included based on filters
   * @param {string} dirName - Name of the directory
   * @param {Object} filters - Filter configuration
   * @returns {boolean} Whether directory should be included
   */
  static _shouldIncludeDirectory(dirName, filters) {
    if (!filters) return true;

    // Skip hidden directories by default
    if (dirName.startsWith('.') && !filters.includeHidden) {
      return false;
    }

    // Check excluded directory patterns
    if (filters.excludeDirectories && filters.excludeDirectories.length > 0) {
      const isExcluded = filters.excludeDirectories.some(pattern => 
        this._matchesPattern(dirName, pattern)
      );
      if (isExcluded) return false;
    }

    return true;
  }

  /**
   * Checks if a string matches a pattern (supports wildcards)
   * @param {string} str - String to test
   * @param {string} pattern - Pattern to match against
   * @returns {boolean} Whether string matches pattern
   */
  static _matchesPattern(str, pattern) {
    // Convert wildcard pattern to regex
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(str);
  }

  /**
   * Validates that a path exists and is accessible
   * @param {string} filePath - Path to validate
   * @returns {Promise<Object>} Validation result
   */
  static async validatePath(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return {
        exists: true,
        isDirectory: stats.isDirectory(),
        isFile: stats.isFile(),
        readable: true,
        writable: true // We'll assume writable for now
      };
    } catch (error) {
      return {
        exists: false,
        error: error.message
      };
    }
  }

  /**
   * Ensures a directory exists, creating it if necessary
   * @param {string} dirPath - Directory path
   * @returns {Promise<void>}
   */
  static async ensureDirectory(dirPath) {
    try {
      await fs.ensureDir(dirPath);
    } catch (error) {
      throw new Error(`Failed to ensure directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Gets the size of a directory
   * @param {string} dirPath - Directory path
   * @returns {Promise<number>} Total size in bytes
   */
  static async getDirectorySize(dirPath) {
    let totalSize = 0;
    
    const files = await this.scanDirectory(dirPath);
    for (const file of files) {
      totalSize += file.size;
    }
    
    return totalSize;
  }
}

module.exports = FileOperations;
