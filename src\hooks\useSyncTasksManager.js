import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSync } from '../contexts/SyncContext';
import { 
  filterTasks, 
  calculateTaskStats, 
  generateFilterOptions 
} from '../utils/taskFilters';

/**
 * Custom Hook: useSyncTasksManager
 * 
 * Clean Code principles:
 * - Single Responsibility: Manages all sync tasks related state and operations
 * - Separation of Concerns: Separates business logic from UI components
 * - DRY: Centralizes task management logic
 * - Clear Interface: Returns well-named functions and state
 */
export const useSyncTasksManager = () => {
  const { 
    syncTasks, 
    createSyncTask, 
    updateSyncTask, 
    deleteSyncTask, 
    startSync, 
    stopSync, 
    activeSyncs 
  } = useSync();
  
  const location = useLocation();
  const navigate = useNavigate();

  // Local state
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Handle URL parameters for deep linking
  useEffect(() => {
    handleUrlParameters();
  }, [location.search, syncTasks]);

  /**
   * Handles URL parameters for actions like create, edit, filter
   */
  const handleUrlParameters = useCallback(() => {
    const params = new URLSearchParams(location.search);
    const action = params.get('action');
    const editId = params.get('edit');
    const filter = params.get('filter');

    if (action === 'new') {
      setShowCreateModal(true);
    }

    if (editId) {
      const taskToEdit = syncTasks.find(task => task.id === editId);
      if (taskToEdit) {
        setEditingTask(taskToEdit);
        setShowEditModal(true);
      }
    }

    if (filter) {
      setFilterStatus(filter);
    }
  }, [location.search, syncTasks]);

  /**
   * Handles task creation with error handling and navigation
   */
  const handleCreateTask = useCallback(async (taskData) => {
    console.log('🔍 Creating task:', taskData);
    try {
      const result = await createSyncTask(taskData);
      console.log('📊 Create result:', result);

      if (result.success) {
        navigate('/sync-tasks', { replace: true });
      }

      return result;
    } catch (error) {
      console.error('❌ Error creating task:', error);
      return { success: false, error: error.message };
    }
  }, [createSyncTask, navigate]);

  /**
   * Handles task editing with error handling and navigation
   */
  const handleEditTask = useCallback(async (taskData) => {
    if (!editingTask) {
      return { success: false, error: 'No task selected for editing' };
    }

    try {
      const result = await updateSyncTask(editingTask.id, taskData);

      if (result.success) {
        closeEditModal();
        navigate('/sync-tasks', { replace: true });
      }

      return result;
    } catch (error) {
      console.error('❌ Error editing task:', error);
      return { success: false, error: error.message };
    }
  }, [editingTask, updateSyncTask, navigate]);

  /**
   * Opens create modal and updates URL
   */
  const openCreateModal = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  /**
   * Closes create modal and cleans up URL
   */
  const closeCreateModal = useCallback(() => {
    setShowCreateModal(false);
    navigate('/sync-tasks', { replace: true });
  }, [navigate]);

  /**
   * Opens edit modal for specific task
   */
  const openEditModal = useCallback((task) => {
    setEditingTask(task);
    setShowEditModal(true);
  }, []);

  /**
   * Closes edit modal and cleans up state
   */
  const closeEditModal = useCallback(() => {
    setShowEditModal(false);
    setEditingTask(null);
    navigate('/sync-tasks', { replace: true });
  }, [navigate]);

  /**
   * Clears all filters and search
   */
  const clearFilters = useCallback(() => {
    setFilterStatus('all');
    setSearchTerm('');
  }, []);

  // Computed values
  const stats = calculateTaskStats(syncTasks, activeSyncs);
  const filteredTasks = filterTasks(syncTasks, filterStatus, searchTerm, activeSyncs);
  const filterOptions = generateFilterOptions(stats, {
    activeSyncs: 'Active',
    completed: 'Completed'
  });

  return {
    // State
    syncTasks,
    filteredTasks,
    activeSyncs,
    stats,
    filterStatus,
    searchTerm,
    showCreateModal,
    showEditModal,
    editingTask,
    filterOptions,

    // Actions
    handleCreateTask,
    handleEditTask,
    openCreateModal,
    closeCreateModal,
    openEditModal,
    closeEditModal,
    startSync,
    stopSync,
    deleteSyncTask,
    setFilterStatus,
    setSearchTerm,
    clearFilters
  };
};
