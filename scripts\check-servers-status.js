const axios = require('axios');

async function checkServersStatus() {
  console.log('🔍 CHECKING SERVERS STATUS\n');

  const servers = [
    {
      name: 'Desktop Server',
      url: 'http://localhost:5000',
      description: 'Desktop app backend server'
    },
    {
      name: 'Web Server',
      url: 'http://localhost:5001',
      description: 'Web management backend server'
    },
    {
      name: 'Web UI',
      url: 'http://localhost:3001',
      description: 'Web management frontend'
    }
  ];

  const results = [];

  for (const server of servers) {
    console.log(`🔍 Checking ${server.name}...`);
    
    try {
      // Try to connect to server
      const response = await axios.get(`${server.url}/health`, { 
        timeout: 3000,
        validateStatus: () => true // Accept any status code
      });
      
      if (response.status === 200) {
        console.log(`✅ ${server.name}: RUNNING`);
        console.log(`   📍 URL: ${server.url}`);
        console.log(`   📊 Status: ${response.status}`);
        if (response.data) {
          console.log(`   📋 Info: ${JSON.stringify(response.data)}`);
        }
        results.push({ ...server, status: 'running', details: response.data });
      } else {
        console.log(`⚠️ ${server.name}: RESPONDING but status ${response.status}`);
        results.push({ ...server, status: 'responding', statusCode: response.status });
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${server.name}: NOT RUNNING`);
        console.log(`   📍 URL: ${server.url}`);
        console.log(`   💡 ${server.description}`);
        results.push({ ...server, status: 'not_running', error: 'Connection refused' });
      } else if (error.code === 'ENOTFOUND') {
        console.log(`❌ ${server.name}: HOST NOT FOUND`);
        results.push({ ...server, status: 'host_not_found', error: error.message });
      } else {
        console.log(`❌ ${server.name}: ERROR - ${error.message}`);
        results.push({ ...server, status: 'error', error: error.message });
      }
    }
    console.log(''); // Empty line for spacing
  }

  // Summary
  console.log('📊 SERVERS STATUS SUMMARY');
  console.log('=' .repeat(50));
  
  const runningServers = results.filter(r => r.status === 'running');
  const notRunningServers = results.filter(r => r.status === 'not_running');
  const errorServers = results.filter(r => r.status === 'error' || r.status === 'host_not_found');

  console.log(`✅ Running: ${runningServers.length}/${servers.length} servers`);
  console.log(`❌ Not Running: ${notRunningServers.length}/${servers.length} servers`);
  console.log(`⚠️ Errors: ${errorServers.length}/${servers.length} servers`);

  if (runningServers.length > 0) {
    console.log('\n✅ RUNNING SERVERS:');
    runningServers.forEach(server => {
      console.log(`   🟢 ${server.name} - ${server.url}`);
    });
  }

  if (notRunningServers.length > 0) {
    console.log('\n❌ NOT RUNNING SERVERS:');
    notRunningServers.forEach(server => {
      console.log(`   🔴 ${server.name} - ${server.url}`);
    });
  }

  if (errorServers.length > 0) {
    console.log('\n⚠️ ERROR SERVERS:');
    errorServers.forEach(server => {
      console.log(`   🟡 ${server.name} - ${server.error}`);
    });
  }

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('=' .repeat(50));

  if (notRunningServers.some(s => s.name === 'Desktop Server')) {
    console.log('🖥️ TO START DESKTOP SERVER:');
    console.log('   npm run start-desktop');
    console.log('   or');
    console.log('   npm run electron-dev');
    console.log('');
  }

  if (notRunningServers.some(s => s.name === 'Web Server')) {
    console.log('🌐 TO START WEB SERVER:');
    console.log('   npm run start-web-management');
    console.log('');
  }

  if (notRunningServers.some(s => s.name === 'Web UI')) {
    console.log('🎨 TO START WEB UI:');
    console.log('   cd web-ui');
    console.log('   npm start');
    console.log('');
  }

  console.log('🔧 FOR FULL SYSTEM:');
  console.log('   Terminal 1: npm run start-desktop');
  console.log('   Terminal 2: npm run start-web-management');
  console.log('   Terminal 3: cd web-ui && npm start');
  console.log('');

  console.log('🧪 FOR TESTING:');
  console.log('   npm run test-token-reuse');
  console.log('   npm run debug-client-status');
  console.log('   npm run test-client-layout');

  return results;
}

async function testAuthentication() {
  console.log('\n🔐 TESTING AUTHENTICATION');
  console.log('=' .repeat(50));

  const credentials = {
    email: '<EMAIL>',
    password: 'password123'
  };

  const servers = [
    { name: 'Desktop', url: 'http://localhost:5000' },
    { name: 'Web', url: 'http://localhost:5001' }
  ];

  for (const server of servers) {
    try {
      console.log(`🔐 Testing ${server.name} server authentication...`);
      
      const response = await axios.post(`${server.url}/api/auth/login`, credentials, { 
        timeout: 5000 
      });
      
      if (response.data.token) {
        console.log(`✅ ${server.name} authentication: SUCCESS`);
        console.log(`   🔑 Token received: ${response.data.token.substring(0, 30)}...`);
        console.log(`   👤 User: ${response.data.user.email}`);
        
        if (response.data.tokenReused !== undefined) {
          console.log(`   🔄 Token reused: ${response.data.tokenReused}`);
        }
        
        if (response.data.sessionInfo) {
          console.log(`   📊 Sessions: ${response.data.sessionInfo.totalSessions}`);
        }
      } else {
        console.log(`❌ ${server.name} authentication: NO TOKEN`);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${server.name} server: NOT RUNNING`);
      } else {
        console.log(`❌ ${server.name} authentication: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
      }
    }
    console.log('');
  }
}

// Run checks
checkServersStatus()
  .then(async (results) => {
    const runningServers = results.filter(r => r.status === 'running');
    
    if (runningServers.length > 0) {
      await testAuthentication();
    }
    
    console.log('\n🎉 SERVER STATUS CHECK COMPLETED!');
    console.log('💡 Start the required servers and run tests again');
  })
  .catch(error => {
    console.error('❌ Check failed:', error);
  });
