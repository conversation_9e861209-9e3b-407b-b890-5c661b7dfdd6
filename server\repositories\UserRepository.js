const BaseRepository = require('./BaseRepository');
const bcrypt = require('bcryptjs');

/**
 * UserRepository Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles user-specific database operations
 * - Encapsulation: Encapsulates user business logic
 * - DRY: Extends base repository functionality
 * - Security: Handles password hashing and validation
 */
class UserRepository extends BaseRepository {
  constructor() {
    super('users', 'id');
  }

  /**
   * Finds a user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User or null if not found
   */
  async findByEmail(email) {
    try {
      return await this.findOne({ email });
    } catch (error) {
      throw new Error(`Failed to find user by email: ${error.message}`);
    }
  }

  /**
   * Creates a new user with hashed password
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user (without password)
   */
  async createUser(userData) {
    try {
      const { password, ...otherData } = userData;
      
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);
      
      // Prepare user data
      const userToCreate = {
        ...otherData,
        password: hashedPassword,
        settings: JSON.stringify(otherData.settings || {}),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const user = await this.create(userToCreate);
      
      // Return user without password
      return this._sanitizeUser(user);
    } catch (error) {
      if (error.message.includes('UNIQUE constraint failed') || error.code === '23505') {
        throw new Error('Email already exists');
      }
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  /**
   * Validates user credentials
   * @param {string} email - User email
   * @param {string} password - Plain text password
   * @returns {Promise<Object|null>} User if valid, null if invalid
   */
  async validateCredentials(email, password) {
    try {
      const user = await this.findByEmail(email);
      
      if (!user) {
        return null;
      }

      const isValidPassword = await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return null;
      }

      // Update last login
      await this.updateLastLogin(user.id);
      
      return this._sanitizeUser(user);
    } catch (error) {
      throw new Error(`Failed to validate credentials: ${error.message}`);
    }
  }

  /**
   * Updates user password
   * @param {number} userId - User ID
   * @param {string} newPassword - New plain text password
   * @returns {Promise<boolean>} Success status
   */
  async updatePassword(userId, newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      
      const result = await this.update(userId, {
        password: hashedPassword,
        updated_at: new Date().toISOString()
      });
      
      return result !== null;
    } catch (error) {
      throw new Error(`Failed to update password: ${error.message}`);
    }
  }

  /**
   * Updates user profile
   * @param {number} userId - User ID
   * @param {Object} profileData - Profile data to update
   * @returns {Promise<Object|null>} Updated user
   */
  async updateProfile(userId, profileData) {
    try {
      const { password, ...safeData } = profileData; // Remove password from profile updates
      
      const dataToUpdate = {
        ...safeData,
        updated_at: new Date().toISOString()
      };

      // Handle settings serialization
      if (dataToUpdate.settings && typeof dataToUpdate.settings === 'object') {
        dataToUpdate.settings = JSON.stringify(dataToUpdate.settings);
      }

      const user = await this.update(userId, dataToUpdate);
      return user ? this._sanitizeUser(user) : null;
    } catch (error) {
      throw new Error(`Failed to update profile: ${error.message}`);
    }
  }

  /**
   * Updates last login timestamp
   * @param {number} userId - User ID
   * @returns {Promise<void>}
   */
  async updateLastLogin(userId) {
    try {
      await this.update(userId, {
        last_login: new Date().toISOString(),
        login_count: this.db.query === 'sqlite' 
          ? 'login_count + 1' 
          : { raw: 'login_count + 1' }
      });
    } catch (error) {
      // Don't throw error for login tracking failures
      console.warn(`Failed to update last login for user ${userId}:`, error.message);
    }
  }

  /**
   * Gets users with pagination and search
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Users and pagination info
   */
  async getUsersWithPagination(filters = {}) {
    try {
      const {
        search,
        role,
        status,
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = filters;

      // Build search criteria
      const criteria = {};
      
      if (role) criteria.role = role;
      if (status) criteria.status = status;

      // Handle search across multiple fields
      let searchQuery = '';
      let searchParams = [];
      
      if (search) {
        searchQuery = `
          AND (email LIKE ? OR name LIKE ? OR 
               COALESCE(first_name, '') LIKE ? OR 
               COALESCE(last_name, '') LIKE ?)
        `;
        const searchPattern = `%${search}%`;
        searchParams = [searchPattern, searchPattern, searchPattern, searchPattern];
      }

      // Get total count
      const countSql = `
        SELECT COUNT(*) as count FROM users 
        WHERE 1=1 ${searchQuery}
      `;
      const countResult = await this.db.query(countSql, searchParams);
      const total = this._getFirstRow(countResult).count;

      // Get users with additional stats
      const usersSql = `
        SELECT
          u.id, u.email, u.name, u.role, u.status,
          u.created_at, u.updated_at, u.last_login, u.login_count,
          (SELECT COUNT(*) FROM sync_tasks WHERE user_id = u.id) as sync_tasks_count,
          (SELECT COUNT(*) FROM sessions WHERE user_id = u.id AND expires_at > datetime('now')) as active_sessions
        FROM users u
        WHERE 1=1 ${searchQuery}
        ORDER BY ${this._sanitizeOrderBy({ column: sortBy, direction: sortOrder })}
        LIMIT ? OFFSET ?
      `;

      const usersResult = await this.db.query(usersSql, [...searchParams, limit, offset]);
      const users = this._getAllRows(usersResult).map(user => this._sanitizeUser(user));

      return {
        users,
        pagination: {
          total,
          limit,
          offset,
          pages: Math.ceil(total / limit),
          currentPage: Math.floor(offset / limit) + 1
        }
      };
    } catch (error) {
      throw new Error(`Failed to get users with pagination: ${error.message}`);
    }
  }

  /**
   * Gets user statistics
   * @param {number} userId - User ID
   * @returns {Promise<Object>} User statistics
   */
  async getUserStats(userId) {
    try {
      const statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM sync_tasks WHERE user_id = ?) as total_tasks,
          (SELECT COUNT(*) FROM sync_tasks WHERE user_id = ? AND status = 'running') as active_tasks,
          (SELECT COUNT(*) FROM sync_history WHERE user_id = ?) as total_syncs,
          (SELECT COUNT(*) FROM sync_history WHERE user_id = ? AND status = 'completed') as successful_syncs,
          (SELECT SUM(files_count) FROM sync_history WHERE user_id = ? AND status = 'completed') as total_files_synced,
          (SELECT SUM(total_size) FROM sync_history WHERE user_id = ? AND status = 'completed') as total_bytes_synced
      `;

      const result = await this.db.query(statsQuery, [userId, userId, userId, userId, userId, userId]);
      const stats = this._getFirstRow(result);

      return {
        totalTasks: stats.total_tasks || 0,
        activeTasks: stats.active_tasks || 0,
        totalSyncs: stats.total_syncs || 0,
        successfulSyncs: stats.successful_syncs || 0,
        totalFilesSynced: stats.total_files_synced || 0,
        totalBytesSynced: stats.total_bytes_synced || 0,
        successRate: stats.total_syncs > 0 
          ? Math.round((stats.successful_syncs / stats.total_syncs) * 100) 
          : 0
      };
    } catch (error) {
      throw new Error(`Failed to get user stats: ${error.message}`);
    }
  }

  /**
   * Removes sensitive data from user object
   * @param {Object} user - User object
   * @returns {Object} Sanitized user object
   */
  _sanitizeUser(user) {
    if (!user) return null;

    const { password, ...sanitizedUser } = user;
    
    // Parse settings if it's a string
    if (sanitizedUser.settings && typeof sanitizedUser.settings === 'string') {
      try {
        sanitizedUser.settings = JSON.parse(sanitizedUser.settings);
      } catch (error) {
        sanitizedUser.settings = {};
      }
    }

    return sanitizedUser;
  }
}

module.exports = UserRepository;
