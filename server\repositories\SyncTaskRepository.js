const BaseRepository = require('./BaseRepository');

/**
 * SyncTaskRepository Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles sync task database operations
 * - Domain-Specific Logic: Encapsulates sync task business rules
 * - Data Integrity: Ensures proper data serialization/deserialization
 */
class SyncTaskRepository extends BaseRepository {
  constructor() {
    super('sync_tasks', 'id');
  }

  /**
   * Creates a new sync task
   * @param {Object} taskData - Task data
   * @returns {Promise<Object>} Created task
   */
  async createTask(taskData) {
    try {
      const taskToCreate = {
        ...taskData,
        filters: JSON.stringify(taskData.filters || []),
        options: JSON.stringify(taskData.options || {}),
        status: taskData.status || 'idle',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const task = await this.create(taskToCreate);
      return this._deserializeTask(task);
    } catch (error) {
      throw new Error(`Failed to create sync task: ${error.message}`);
    }
  }

  /**
   * Updates a sync task
   * @param {number} taskId - Task ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated task
   */
  async updateTask(taskId, updateData) {
    try {
      const dataToUpdate = { ...updateData };
      
      // Serialize JSON fields
      if (dataToUpdate.filters) {
        dataToUpdate.filters = JSON.stringify(dataToUpdate.filters);
      }
      if (dataToUpdate.options) {
        dataToUpdate.options = JSON.stringify(dataToUpdate.options);
      }
      
      dataToUpdate.updated_at = new Date().toISOString();

      const task = await this.update(taskId, dataToUpdate);
      return task ? this._deserializeTask(task) : null;
    } catch (error) {
      throw new Error(`Failed to update sync task: ${error.message}`);
    }
  }

  /**
   * Gets tasks by user ID
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} User's sync tasks
   */
  async getTasksByUser(userId, options = {}) {
    try {
      const tasks = await this.findBy({ user_id: userId }, options);
      return tasks.map(task => this._deserializeTask(task));
    } catch (error) {
      throw new Error(`Failed to get tasks for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Gets tasks by client ID
   * @param {string} clientId - Client ID
   * @param {number} userId - User ID (for security)
   * @returns {Promise<Array>} Client's sync tasks
   */
  async getTasksByClient(clientId, userId) {
    try {
      const tasks = await this.findBy({ 
        client_id: clientId, 
        user_id: userId 
      });
      return tasks.map(task => this._deserializeTask(task));
    } catch (error) {
      throw new Error(`Failed to get tasks for client ${clientId}: ${error.message}`);
    }
  }

  /**
   * Gets task with detailed information
   * @param {number} taskId - Task ID
   * @param {number} userId - User ID (for security)
   * @returns {Promise<Object|null>} Task with additional details
   */
  async getTaskWithDetails(taskId, userId) {
    try {
      const sql = `
        SELECT 
          st.*,
          (SELECT COUNT(*) FROM sync_history WHERE task_id = st.id) as sync_count,
          (SELECT MAX(started_at) FROM sync_history WHERE task_id = st.id) as last_sync_time,
          (SELECT status FROM sync_history WHERE task_id = st.id ORDER BY started_at DESC LIMIT 1) as last_sync_status,
          (SELECT COUNT(*) FROM file_changes WHERE task_id = st.id) as file_changes_count
        FROM sync_tasks st
        WHERE st.id = ? AND st.user_id = ?
      `;

      const result = await this.db.query(sql, [taskId, userId]);
      const task = this._getFirstRow(result);
      
      return task ? this._deserializeTask(task) : null;
    } catch (error) {
      throw new Error(`Failed to get task details: ${error.message}`);
    }
  }

  /**
   * Gets tasks by status
   * @param {string} status - Task status
   * @param {number} userId - User ID (optional)
   * @returns {Promise<Array>} Tasks with specified status
   */
  async getTasksByStatus(status, userId = null) {
    try {
      const criteria = { status };
      if (userId) {
        criteria.user_id = userId;
      }

      const tasks = await this.findBy(criteria);
      return tasks.map(task => this._deserializeTask(task));
    } catch (error) {
      throw new Error(`Failed to get tasks by status ${status}: ${error.message}`);
    }
  }

  /**
   * Gets scheduled tasks that need to run
   * @returns {Promise<Array>} Tasks ready for execution
   */
  async getScheduledTasks() {
    try {
      const sql = `
        SELECT * FROM sync_tasks 
        WHERE schedule IS NOT NULL 
        AND schedule != '' 
        AND status IN ('idle', 'scheduled')
        AND (last_sync IS NULL OR last_sync < datetime('now', '-1 hour'))
      `;

      const result = await this.db.query(sql);
      const tasks = this._getAllRows(result);
      return tasks.map(task => this._deserializeTask(task));
    } catch (error) {
      throw new Error(`Failed to get scheduled tasks: ${error.message}`);
    }
  }

  /**
   * Updates task status
   * @param {number} taskId - Task ID
   * @param {string} status - New status
   * @param {Object} additionalData - Additional data to update
   * @returns {Promise<Object|null>} Updated task
   */
  async updateTaskStatus(taskId, status, additionalData = {}) {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString(),
        ...additionalData
      };

      return await this.updateTask(taskId, updateData);
    } catch (error) {
      throw new Error(`Failed to update task status: ${error.message}`);
    }
  }

  /**
   * Updates task sync statistics
   * @param {number} taskId - Task ID
   * @param {Object} stats - Sync statistics
   * @returns {Promise<Object|null>} Updated task
   */
  async updateTaskStats(taskId, stats) {
    try {
      const updateData = {
        last_sync: new Date().toISOString(),
        files_count: stats.filesCount || 0,
        total_size: stats.totalSize || 0,
        updated_at: new Date().toISOString()
      };

      return await this.updateTask(taskId, updateData);
    } catch (error) {
      throw new Error(`Failed to update task stats: ${error.message}`);
    }
  }

  /**
   * Gets task statistics for a user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Task statistics
   */
  async getTaskStats(userId) {
    try {
      const sql = `
        SELECT
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN status = 'running' THEN 1 END) as active_tasks,
          COUNT(CASE WHEN status = 'idle' THEN 1 END) as idle_tasks,
          COUNT(CASE WHEN status = 'error' THEN 1 END) as error_tasks,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
          COUNT(CASE WHEN schedule IS NOT NULL AND schedule != '' THEN 1 END) as scheduled_tasks,
          SUM(files_count) as total_files,
          SUM(total_size) as total_size
        FROM sync_tasks
        WHERE user_id = ?
      `;

      const result = await this.db.query(sql, [userId]);
      const stats = this._getFirstRow(result);

      return {
        totalTasks: stats.total_tasks || 0,
        activeTasks: stats.active_tasks || 0,
        idleTasks: stats.idle_tasks || 0,
        errorTasks: stats.error_tasks || 0,
        completedTasks: stats.completed_tasks || 0,
        scheduledTasks: stats.scheduled_tasks || 0,
        totalFiles: stats.total_files || 0,
        totalSize: stats.total_size || 0
      };
    } catch (error) {
      throw new Error(`Failed to get task stats: ${error.message}`);
    }
  }

  /**
   * Searches tasks by name or path
   * @param {string} searchTerm - Search term
   * @param {number} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Matching tasks
   */
  async searchTasks(searchTerm, userId, options = {}) {
    try {
      const sql = `
        SELECT * FROM sync_tasks
        WHERE user_id = ?
        AND (
          name LIKE ? OR
          source_path LIKE ? OR
          destination_path LIKE ?
        )
        ORDER BY name ASC
        ${options.limit ? `LIMIT ${parseInt(options.limit)}` : ''}
      `;

      const searchPattern = `%${searchTerm}%`;
      const result = await this.db.query(sql, [
        userId, 
        searchPattern, 
        searchPattern, 
        searchPattern
      ]);

      const tasks = this._getAllRows(result);
      return tasks.map(task => this._deserializeTask(task));
    } catch (error) {
      throw new Error(`Failed to search tasks: ${error.message}`);
    }
  }

  /**
   * Deletes a task and its related data
   * @param {number} taskId - Task ID
   * @param {number} userId - User ID (for security)
   * @returns {Promise<boolean>} Success status
   */
  async deleteTask(taskId, userId) {
    try {
      // Verify ownership
      const task = await this.findOne({ id: taskId, user_id: userId });
      if (!task) {
        return false;
      }

      // Delete related data first (due to foreign key constraints)
      await this.db.query('DELETE FROM file_changes WHERE task_id = ?', [taskId]);
      await this.db.query('DELETE FROM sync_history WHERE task_id = ?', [taskId]);
      await this.db.query('DELETE FROM file_versions WHERE task_id = ?', [taskId]);
      await this.db.query('DELETE FROM file_conflicts WHERE task_id = ?', [taskId]);

      // Delete the task
      return await this.delete(taskId);
    } catch (error) {
      throw new Error(`Failed to delete task: ${error.message}`);
    }
  }

  /**
   * Deserializes JSON fields in task object
   * @param {Object} task - Raw task from database
   * @returns {Object} Task with parsed JSON fields
   */
  _deserializeTask(task) {
    if (!task) return null;

    const deserializedTask = { ...task };

    // Parse JSON fields
    if (typeof task.filters === 'string') {
      try {
        deserializedTask.filters = JSON.parse(task.filters);
      } catch (error) {
        deserializedTask.filters = [];
      }
    }

    if (typeof task.options === 'string') {
      try {
        deserializedTask.options = JSON.parse(task.options);
      } catch (error) {
        deserializedTask.options = {};
      }
    }

    return deserializedTask;
  }
}

module.exports = SyncTaskRepository;
