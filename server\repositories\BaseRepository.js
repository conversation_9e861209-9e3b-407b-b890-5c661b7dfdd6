const { getDatabase } = require('../database/init');

/**
 * BaseRepository Class
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles basic CRUD operations
 * - DRY: Provides reusable database operations
 * - Separation of Concerns: Separates data access from business logic
 * - Interface Segregation: Provides focused database interface
 */
class BaseRepository {
  constructor(tableName, primaryKey = 'id') {
    this.tableName = tableName;
    this.primaryKey = primaryKey;
    this.db = getDatabase();
  }

  /**
   * Finds a record by ID
   * @param {number|string} id - Record ID
   * @returns {Promise<Object|null>} Record or null if not found
   */
  async findById(id) {
    try {
      const result = await this.db.query(
        `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`,
        [id]
      );
      return this._getFirstRow(result);
    } catch (error) {
      throw new Error(`Failed to find ${this.tableName} by ID ${id}: ${error.message}`);
    }
  }

  /**
   * Finds records by criteria
   * @param {Object} criteria - Search criteria
   * @param {Object} options - Query options (limit, offset, orderBy)
   * @returns {Promise<Array>} Array of records
   */
  async findBy(criteria = {}, options = {}) {
    try {
      const { whereClause, params } = this._buildWhereClause(criteria);
      const { limit, offset, orderBy } = options;
      
      let sql = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
      }
      
      if (orderBy) {
        sql += ` ORDER BY ${this._sanitizeOrderBy(orderBy)}`;
      }
      
      if (limit) {
        sql += ` LIMIT ${parseInt(limit)}`;
        if (offset) {
          sql += ` OFFSET ${parseInt(offset)}`;
        }
      }

      const result = await this.db.query(sql, params);
      return this._getAllRows(result);
    } catch (error) {
      throw new Error(`Failed to find ${this.tableName} records: ${error.message}`);
    }
  }

  /**
   * Finds one record by criteria
   * @param {Object} criteria - Search criteria
   * @returns {Promise<Object|null>} Record or null if not found
   */
  async findOne(criteria) {
    const records = await this.findBy(criteria, { limit: 1 });
    return records.length > 0 ? records[0] : null;
  }

  /**
   * Gets all records
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of all records
   */
  async findAll(options = {}) {
    return this.findBy({}, options);
  }

  /**
   * Creates a new record
   * @param {Object} data - Record data
   * @returns {Promise<Object>} Created record with ID
   */
  async create(data) {
    try {
      const { columns, placeholders, values } = this._buildInsertClause(data);
      
      const sql = `INSERT INTO ${this.tableName} (${columns}) VALUES (${placeholders})`;
      const result = await this.db.query(sql, values);
      
      const insertId = result.insertId || result.lastInsertRowid;
      return this.findById(insertId);
    } catch (error) {
      throw new Error(`Failed to create ${this.tableName} record: ${error.message}`);
    }
  }

  /**
   * Updates a record by ID
   * @param {number|string} id - Record ID
   * @param {Object} data - Updated data
   * @returns {Promise<Object|null>} Updated record or null if not found
   */
  async update(id, data) {
    try {
      const { setClause, values } = this._buildUpdateClause(data);
      
      const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`;
      const result = await this.db.query(sql, [...values, id]);
      
      if (result.rowCount === 0 || result.changes === 0) {
        return null;
      }
      
      return this.findById(id);
    } catch (error) {
      throw new Error(`Failed to update ${this.tableName} record ${id}: ${error.message}`);
    }
  }

  /**
   * Deletes a record by ID
   * @param {number|string} id - Record ID
   * @returns {Promise<boolean>} True if deleted, false if not found
   */
  async delete(id) {
    try {
      const sql = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
      const result = await this.db.query(sql, [id]);
      
      return (result.rowCount || result.changes) > 0;
    } catch (error) {
      throw new Error(`Failed to delete ${this.tableName} record ${id}: ${error.message}`);
    }
  }

  /**
   * Counts records matching criteria
   * @param {Object} criteria - Search criteria
   * @returns {Promise<number>} Count of matching records
   */
  async count(criteria = {}) {
    try {
      const { whereClause, params } = this._buildWhereClause(criteria);
      
      let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      if (whereClause) {
        sql += ` WHERE ${whereClause}`;
      }

      const result = await this.db.query(sql, params);
      const row = this._getFirstRow(result);
      return row ? row.count : 0;
    } catch (error) {
      throw new Error(`Failed to count ${this.tableName} records: ${error.message}`);
    }
  }

  /**
   * Checks if a record exists
   * @param {Object} criteria - Search criteria
   * @returns {Promise<boolean>} True if exists, false otherwise
   */
  async exists(criteria) {
    const count = await this.count(criteria);
    return count > 0;
  }

  /**
   * Executes a custom query
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise<Array>} Query results
   */
  async query(sql, params = []) {
    try {
      const result = await this.db.query(sql, params);
      return this._getAllRows(result);
    } catch (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
  }

  /**
   * Builds WHERE clause from criteria object
   * @param {Object} criteria - Search criteria
   * @returns {Object} WHERE clause and parameters
   */
  _buildWhereClause(criteria) {
    const conditions = [];
    const params = [];

    for (const [key, value] of Object.entries(criteria)) {
      if (value === null) {
        conditions.push(`${key} IS NULL`);
      } else if (value === undefined) {
        continue; // Skip undefined values
      } else if (Array.isArray(value)) {
        const placeholders = value.map(() => '?').join(', ');
        conditions.push(`${key} IN (${placeholders})`);
        params.push(...value);
      } else if (typeof value === 'object' && value.operator) {
        // Support for complex conditions like { operator: 'LIKE', value: '%test%' }
        conditions.push(`${key} ${value.operator} ?`);
        params.push(value.value);
      } else {
        conditions.push(`${key} = ?`);
        params.push(value);
      }
    }

    return {
      whereClause: conditions.length > 0 ? conditions.join(' AND ') : '',
      params
    };
  }

  /**
   * Builds INSERT clause from data object
   * @param {Object} data - Data to insert
   * @returns {Object} Columns, placeholders, and values
   */
  _buildInsertClause(data) {
    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    return { columns, placeholders, values };
  }

  /**
   * Builds UPDATE SET clause from data object
   * @param {Object} data - Data to update
   * @returns {Object} SET clause and values
   */
  _buildUpdateClause(data) {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = Object.values(data);

    return { setClause, values };
  }

  /**
   * Sanitizes ORDER BY clause
   * @param {string|Object} orderBy - Order by specification
   * @returns {string} Sanitized ORDER BY clause
   */
  _sanitizeOrderBy(orderBy) {
    if (typeof orderBy === 'string') {
      return orderBy.replace(/[^a-zA-Z0-9_,\s]/g, '');
    }
    
    if (typeof orderBy === 'object') {
      const { column, direction = 'ASC' } = orderBy;
      const safeColumn = column.replace(/[^a-zA-Z0-9_]/g, '');
      const safeDirection = ['ASC', 'DESC'].includes(direction.toUpperCase()) ? direction.toUpperCase() : 'ASC';
      return `${safeColumn} ${safeDirection}`;
    }
    
    return 'id ASC';
  }

  /**
   * Gets first row from query result
   * @param {Object} result - Query result
   * @returns {Object|null} First row or null
   */
  _getFirstRow(result) {
    const rows = result.rows || result;
    return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;
  }

  /**
   * Gets all rows from query result
   * @param {Object} result - Query result
   * @returns {Array} All rows
   */
  _getAllRows(result) {
    return result.rows || result || [];
  }
}

module.exports = BaseRepository;
