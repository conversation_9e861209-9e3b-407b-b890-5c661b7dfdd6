import React from 'react';
import TaskCard from './TaskCard';
import { 
  FolderIcon, 
  PlusIcon, 
  MagnifyingGlassIcon 
} from '../UI/Icons';
import { getFilterDisplayTitle } from '../../utils/taskFilters';

/**
 * TasksList Component
 * 
 * Clean Code principles:
 * - Single Responsibility: Only handles task list rendering
 * - DRY: Reusable empty states
 * - Clear naming: Self-documenting component and prop names
 */
const TasksList = ({
  tasks,
  allTasks,
  activeSyncs,
  filterStatus,
  onCreateTask,
  onEditTask,
  onStartSync,
  onStopSync,
  onDeleteTask,
  onClearFilters,
  translations
}) => {
  const hasNoTasks = allTasks.length === 0;
  const hasNoFilteredTasks = tasks.length === 0 && allTasks.length > 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <TasksListHeader 
        filterStatus={filterStatus}
        filteredCount={tasks.length}
        totalCount={allTasks.length}
        translations={translations}
      />

      <TasksListContent
        hasNoTasks={hasNoTasks}
        hasNoFilteredTasks={hasNoFilteredTasks}
        tasks={tasks}
        activeSyncs={activeSyncs}
        onCreateTask={onCreateTask}
        onEditTask={onEditTask}
        onStartSync={onStartSync}
        onStopSync={onStopSync}
        onDeleteTask={onDeleteTask}
        onClearFilters={onClearFilters}
        translations={translations}
      />
    </div>
  );
};

/**
 * TasksListHeader Component
 * 
 * Single responsibility: Display list header with title and count
 */
const TasksListHeader = ({ 
  filterStatus, 
  filteredCount, 
  totalCount, 
  translations 
}) => (
  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
    <div className="flex items-center justify-between">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
        {getFilterDisplayTitle(filterStatus, translations)}
      </h2>
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {filteredCount} of {totalCount} tasks
      </span>
    </div>
  </div>
);

/**
 * TasksListContent Component
 * 
 * Single responsibility: Render appropriate content based on state
 */
const TasksListContent = ({
  hasNoTasks,
  hasNoFilteredTasks,
  tasks,
  activeSyncs,
  onCreateTask,
  onEditTask,
  onStartSync,
  onStopSync,
  onDeleteTask,
  onClearFilters,
  translations
}) => {
  if (hasNoTasks) {
    return (
      <EmptyTasksState 
        onCreateTask={onCreateTask}
        translations={translations}
      />
    );
  }

  if (hasNoFilteredTasks) {
    return (
      <NoFilteredTasksState 
        onClearFilters={onClearFilters}
        translations={translations}
      />
    );
  }

  return (
    <TasksGrid
      tasks={tasks}
      activeSyncs={activeSyncs}
      onEditTask={onEditTask}
      onStartSync={onStartSync}
      onStopSync={onStopSync}
      onDeleteTask={onDeleteTask}
    />
  );
};

/**
 * EmptyTasksState Component
 * 
 * Single responsibility: Display empty state when no tasks exist
 */
const EmptyTasksState = ({ onCreateTask, translations }) => (
  <div className="text-center py-12">
    <FolderIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
      {translations('noSyncTasks')}
    </h3>
    <p className="text-gray-500 dark:text-gray-400 mb-6">
      {translations('createFirstTask')}
    </p>
    <CreateTaskButton 
      onClick={onCreateTask}
      text={translations('createTask')}
    />
  </div>
);

/**
 * NoFilteredTasksState Component
 * 
 * Single responsibility: Display state when filters return no results
 */
const NoFilteredTasksState = ({ onClearFilters, translations }) => (
  <div className="text-center py-12">
    <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
      {translations('noTasksFound')}
    </h3>
    <p className="text-gray-500 dark:text-gray-400 mb-6">
      Try adjusting your filters or search terms
    </p>
    <ClearFiltersButton 
      onClick={onClearFilters}
      text={translations('clearFilters')}
    />
  </div>
);

/**
 * TasksGrid Component
 * 
 * Single responsibility: Render grid of task cards
 */
const TasksGrid = ({
  tasks,
  activeSyncs,
  onEditTask,
  onStartSync,
  onStopSync,
  onDeleteTask
}) => (
  <div className="p-6">
    <div className="space-y-4">
      {tasks.map((task) => (
        <TaskCard
          key={task.id}
          task={task}
          isActive={activeSyncs.has(task.id)}
          onStart={() => onStartSync(task.id)}
          onStop={() => onStopSync(task.id)}
          onEdit={() => onEditTask(task)}
          onDelete={() => onDeleteTask(task.id)}
        />
      ))}
    </div>
  </div>
);

/**
 * CreateTaskButton Component
 * 
 * Single responsibility: Render create task button
 */
const CreateTaskButton = ({ onClick, text }) => (
  <button
    onClick={onClick}
    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
    aria-label="Create new sync task"
  >
    <PlusIcon className="w-4 h-4 mr-2" />
    {text}
  </button>
);

/**
 * ClearFiltersButton Component
 * 
 * Single responsibility: Render clear filters button
 */
const ClearFiltersButton = ({ onClick, text }) => (
  <button
    onClick={onClick}
    className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
    aria-label="Clear all filters"
  >
    {text}
  </button>
);

export default TasksList;
