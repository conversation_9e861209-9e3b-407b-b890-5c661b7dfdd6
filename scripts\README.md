# Scripts Directory

This directory contains utility scripts for SyncMasterPro database management.

## 📁 Available Scripts

### 🔧 Setup Scripts

- **`setup.js`** - Initial application setup (creates directories, .env file)

### 🗄️ Database Scripts

- **`create-tables.js`** - Create PostgreSQL tables and indexes
- **`simple-sync.js`** - Sync data from SQLite to PostgreSQL
- **`test-postgresql-connection.js`** - Test PostgreSQL connection and list databases

## 🚀 Usage

### Initial Setup

```bash
npm run setup
```

### PostgreSQL Management

```bash
# Test PostgreSQL connection
npm run test-pg

# Create tables in PostgreSQL
npm run create-tables

# Sync data from SQLite to PostgreSQL
npm run sync-to-pg
```

## 📋 Script Details

### create-tables.js

- Creates all required tables in PostgreSQL
- Creates indexes for performance
- Verifies table creation
- Shows table row counts

### simple-sync.js

- Syncs users from SQLite to PostgreSQL
- Handles conflicts with ON CONFLICT clause
- Shows sync progress and results

### test-postgresql-connection.js

- Tests connection to PostgreSQL server
- Lists available databases
- Checks if SyncMasterPro database exists
- Provides troubleshooting information

## 🔧 Configuration

Scripts use the following PostgreSQL connection:

- Host: *************
- Port: 5432
- Database: syncmasterpro
- User: pi
- Password: ubuntu

## 🚨 Troubleshooting

If scripts fail:

1. Check PostgreSQL server is running
1. Verify network connectivity to *************
1. Ensure user 'pi' has proper permissions
1. Check if database 'syncmasterpro' exists
