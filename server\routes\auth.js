const express = require('express');
const UserService = require('../services/UserService');
const authMiddleware = require('../middleware/auth');
const { validateBody, schemas } = require('../middleware/validation');
const { addResponseHelpers } = require('../utils/responseHelper');

const router = express.Router();
const userService = new UserService();

// Add response helpers to all routes
router.use(addResponseHelpers);
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// Register
router.post('/register', validateBody(schemas.userRegistration), async (req, res) => {
  try {
    const { email, password, name } = req.body;

    const result = await userService.registerUser({
      email,
      password,
      name
    });

    res.handleServiceResult(result, 201);
  } catch (error) {
    console.error('Registration error:', error);
    res.sendInternalError('Failed to register user', error);
  }
});

// Login
router.post('/login', validateBody(schemas.userLogin), async (req, res) => {
  try {
    const { email, password } = req.body;

    const result = await userService.authenticateUser(email, password);
    
    const db = getDatabase();
    
    // Find user
    const result = await db.query(
      'SELECT id, email, password, name, avatar, settings, created_at FROM users WHERE email = ?',
      [email]
    );
    
    if (result.rows.length === 0) {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      });
    }
    
    const user = result.rows[0];
    
    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Invalid email or password'
      });
    }
    
    // Clean up expired sessions only (allow multiple concurrent sessions)
    await db.query('DELETE FROM sessions WHERE user_id = ? AND expires_at < ?',
      [user.id, new Date().toISOString()]);

    // Check for existing valid session to reuse
    const existingSession = await db.query(`
      SELECT token, expires_at FROM sessions
      WHERE user_id = ? AND expires_at > ?
      ORDER BY created_at DESC
      LIMIT 1
    `, [user.id, new Date().toISOString()]);

    let token;
    let expiresAt;

    let tokenReused = false;

    if (existingSession.rows.length > 0) {
      // Reuse existing valid token
      token = existingSession.rows[0].token;
      expiresAt = existingSession.rows[0].expires_at;
      tokenReused = true;
      console.log(`🔄 Reusing existing token for user ${user.email} (expires: ${expiresAt})`);
    } else {
      // Generate new JWT token only if no valid session exists
      token = jwt.sign(
        { userId: user.id, email: user.email },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );

      // Store new session
      expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days
      await db.query(
        'INSERT INTO sessions (user_id, token, expires_at) VALUES (?, ?, ?)',
        [user.id, token, expiresAt]
      );
      tokenReused = false;
      console.log(`🆕 Created new token for user ${user.email} (expires: ${expiresAt})`);
    }

    // Optional: Limit maximum concurrent sessions per user (e.g., 10 sessions)
    const sessionCount = await db.query('SELECT COUNT(*) as count FROM sessions WHERE user_id = ?', [user.id]);
    const maxSessions = 10;

    if (sessionCount.rows[0].count > maxSessions) {
      console.log(`⚠️ User ${user.email} has ${sessionCount.rows[0].count} sessions, cleaning up oldest ones`);
      // Remove oldest sessions to make room for new one
      await db.query(`
        DELETE FROM sessions
        WHERE user_id = ? AND token != ?
        ORDER BY created_at ASC
        LIMIT ?
      `, [user.id, token, sessionCount.rows[0].count - maxSessions]);
    }

    // Log successful multi-client login
    const currentSessionCount = await db.query('SELECT COUNT(*) as count FROM sessions WHERE user_id = ?', [user.id]);
    console.log(`✅ Multi-client login: User ${user.email} now has ${currentSessionCount.rows[0].count} active sessions`);
    
    // Return user data (without password)
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      settings: typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings,
      createdAt: user.created_at
    };
    
    // Initialize Client Manager for desktop mode
    if (process.env.ELECTRON_ENV === 'true') {
      try {
        const extendedSyncEngine = req.app.get('extendedSyncEngine');
        if (extendedSyncEngine) {
          console.log('🖥️ Initializing Client Manager for desktop user:', userData.email);
          await extendedSyncEngine.initializeClientManager(userData, token);
        }
      } catch (error) {
        console.error('❌ Failed to initialize Client Manager:', error);
        // Don't fail login if client manager fails
      }
    }

    res.json({
      message: 'Login successful',
      token,
      user: userData,
      tokenReused,
      sessionInfo: {
        expiresAt,
        totalSessions: currentSessionCount.rows[0].count
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to login'
    });
  }
});

// Logout
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    console.log('🚪 Processing logout request for user:', req.user?.email);

    // Handle client manager logout (check for any client manager, not just desktop mode)
    const extendedSyncEngine = req.app.get('extendedSyncEngine');
    console.log('🔍 ExtendedSyncEngine available:', !!extendedSyncEngine);
    console.log('🔍 ClientManager available:', !!(extendedSyncEngine && extendedSyncEngine.clientManager));

    if (extendedSyncEngine && extendedSyncEngine.clientManager) {
      try {
        console.log('🚪 Calling client manager logout for user:', req.user.email);
        await extendedSyncEngine.clientManager.logout();
        console.log('✅ Client manager logout completed');
      } catch (error) {
        console.error('❌ Failed to logout client manager:', error);
        // Don't fail logout if client manager fails
      }
    } else {
      console.log('⚠️ Client manager not available - skipping client logout');
    }

    if (token) {
      const db = getDatabase();
      await db.query('DELETE FROM sessions WHERE token = ?', [token]);
    }

    res.json({ message: 'Logout successful' });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to logout'
    });
  }
});

// Verify token
router.get('/verify', authMiddleware, async (req, res) => {
  try {
    const db = getDatabase();
    
    // Get user data
    const result = await db.query(
      'SELECT id, email, name, avatar, settings, created_at FROM users WHERE id = ?',
      [req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(401).json({
        valid: false,
        message: 'User not found'
      });
    }
    
    const user = result.rows[0];
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      settings: typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings,
      createdAt: user.created_at
    };
    
    res.json({
      valid: true,
      user: userData
    });
    
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(500).json({
      valid: false,
      message: 'Failed to verify token'
    });
  }
});

// Update profile
router.put('/profile', authMiddleware, async (req, res) => {
  try {
    const { name, avatar, settings } = req.body;
    const db = getDatabase();
    
    // Build update query dynamically
    const updates = [];
    const values = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      values.push(name);
    }
    
    if (avatar !== undefined) {
      updates.push('avatar = ?');
      values.push(avatar);
    }
    
    if (settings !== undefined) {
      updates.push('settings = ?');
      values.push(JSON.stringify(settings));
    }
    
    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No valid fields to update'
      });
    }
    
    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(req.userId);
    
    await db.query(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      values
    );
    
    // Get updated user data
    const result = await db.query(
      'SELECT id, email, name, avatar, settings, created_at FROM users WHERE id = ?',
      [req.userId]
    );
    
    const user = result.rows[0];
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      settings: typeof user.settings === 'string' ? JSON.parse(user.settings) : user.settings,
      createdAt: user.created_at
    };
    
    res.json({
      message: 'Profile updated successfully',
      user: userData
    });
    
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update profile'
    });
  }
});

// Forgot password - Request reset
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Email is required'
      });
    }

    const db = getDatabase();

    // Check if user exists
    const result = await db.query(
      'SELECT id, email, name FROM users WHERE email = ?',
      [email]
    );

    if (result.rows.length === 0) {
      // Don't reveal if email exists or not for security
      return res.json({
        message: 'If an account with that email exists, we have sent a password reset link.'
      });
    }

    const user = result.rows[0];

    // Generate reset token
    const resetToken = require('crypto').randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000).toISOString(); // 1 hour

    // Store reset token
    await db.query(
      'INSERT OR REPLACE INTO password_resets (user_id, token, expires_at, created_at) VALUES (?, ?, ?, ?)',
      [user.id, resetToken, resetTokenExpiry, new Date().toISOString()]
    );

    // TODO: Send email with reset link
    // For now, we'll just log the token (in production, send email)
    console.log(`🔑 Password reset token for ${email}: ${resetToken}`);
    console.log(`🔗 Reset link: http://localhost:3000/reset-password?token=${resetToken}`);

    res.json({
      message: 'If an account with that email exists, we have sent a password reset link.',
      // In development, include token for testing
      ...(process.env.NODE_ENV === 'development' && { resetToken })
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to process password reset request'
    });
  }
});

// Reset password with token
router.post('/reset-password', async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Reset token and new password are required'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'New password must be at least 6 characters long'
      });
    }

    const db = getDatabase();

    // Find valid reset token
    const result = await db.query(
      'SELECT user_id FROM password_resets WHERE token = ? AND expires_at > ? AND used_at IS NULL',
      [token, new Date().toISOString()]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({
        error: 'Invalid Token',
        message: 'Invalid or expired reset token'
      });
    }

    const userId = result.rows[0].user_id;

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await db.query(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedPassword, userId]
    );

    // Mark token as used
    await db.query(
      'UPDATE password_resets SET used_at = CURRENT_TIMESTAMP WHERE token = ?',
      [token]
    );

    // Invalidate all sessions for this user (security requirement for password reset)
    await db.query('DELETE FROM sessions WHERE user_id = ?', [userId]);
    console.log(`🔐 Password reset: All sessions invalidated for user ${userId}`);

    res.json({ message: 'Password reset successfully' });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to reset password'
    });
  }
});

// Change password
router.put('/password', authMiddleware, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Current password and new password are required'
      });
    }
    
    if (newPassword.length < 6) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'New password must be at least 6 characters long'
      });
    }
    
    const db = getDatabase();
    
    // Get current password hash
    const result = await db.query(
      'SELECT password FROM users WHERE id = ?',
      [req.userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }
    
    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, result.rows[0].password);
    
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Current password is incorrect'
      });
    }
    
    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update password
    await db.query(
      'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [hashedPassword, req.userId]
    );
    
    // Option 1: Invalidate only current session (recommended)
    await db.query('DELETE FROM sessions WHERE token = ?', [req.token]);
    console.log(`🔐 Password changed: Current session invalidated for user ${req.userId}`);

    // Option 2: Uncomment below to invalidate ALL sessions (more secure but logs out all devices)
    // await db.query('DELETE FROM sessions WHERE user_id = ?', [req.userId]);
    // console.log(`🔐 Password changed: All sessions invalidated for user ${req.userId}`);
    
    res.json({ message: 'Password changed successfully' });
    
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to change password'
    });
  }
});

// Get user sessions
router.get('/sessions', authMiddleware, async (req, res) => {
  try {
    const db = getDatabase();

    const result = await db.query(`
      SELECT
        token,
        created_at,
        expires_at,
        CASE WHEN token = ? THEN true ELSE false END as is_current
      FROM sessions
      WHERE user_id = ? AND expires_at > ?
      ORDER BY created_at DESC
    `, [req.token, req.userId, new Date().toISOString()]);

    const sessions = result.rows.map(session => ({
      id: session.token.substring(0, 8) + '...',
      createdAt: session.created_at,
      expiresAt: session.expires_at,
      isCurrent: session.is_current,
      // Don't expose full token for security
      tokenPreview: session.token.substring(0, 20) + '...'
    }));

    res.json({
      sessions,
      total: sessions.length
    });

  } catch (error) {
    console.error('Get sessions error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get sessions'
    });
  }
});

// Revoke specific session
router.delete('/sessions/:tokenId', authMiddleware, async (req, res) => {
  try {
    const { tokenId } = req.params;
    const db = getDatabase();

    // Find session by token prefix
    const result = await db.query(`
      SELECT token FROM sessions
      WHERE user_id = ? AND token LIKE ?
    `, [req.userId, tokenId + '%']);

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Session not found'
      });
    }

    const fullToken = result.rows[0].token;

    // Don't allow revoking current session via this endpoint
    if (fullToken === req.token) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Cannot revoke current session. Use logout instead.'
      });
    }

    await db.query('DELETE FROM sessions WHERE token = ?', [fullToken]);

    res.json({ message: 'Session revoked successfully' });

  } catch (error) {
    console.error('Revoke session error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to revoke session'
    });
  }
});

// Revoke all other sessions (keep current)
router.post('/sessions/revoke-others', authMiddleware, async (req, res) => {
  try {
    const db = getDatabase();

    const result = await db.query(
      'DELETE FROM sessions WHERE user_id = ? AND token != ?',
      [req.userId, req.token]
    );

    const deletedCount = result.affectedRows || result.rowCount || 0;

    res.json({
      message: 'Other sessions revoked successfully',
      revokedCount: deletedCount
    });

  } catch (error) {
    console.error('Revoke other sessions error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to revoke other sessions'
    });
  }
});

module.exports = router;
