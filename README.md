# SyncMasterPro

Professional File & Folder Synchronization Software built with Electron,
React, and Node.js.

## Features

### 🚀 Core Features

- **Real-time File Synchronization** - Automatic sync with file system monitoring
- **Bidirectional Sync** - Two-way synchronization between folders
- **Selective Sync** - Choose specific files and folders to sync
- **Conflict Resolution** - Smart handling of file conflicts
- **Scheduling** - Set up automatic sync schedules
- **History Tracking** - Complete sync history with detailed logs

### 💻 Desktop Application

- **Cross-platform** - Windows, macOS, and Linux support
- **Offline Operation** - Works without internet connection
- **System Tray Integration** - Background operation with system tray
- **Native Notifications** - Desktop notifications for sync events
- **File Explorer Integration** - Context menu integration

### 🌐 Web Management

- **Remote Management** - Manage sync tasks from anywhere
- **User Accounts** - Multi-user support with authentication
- **Cloud Dashboard** - Web-based control panel
- **Real-time Status** - Live sync status monitoring
- **Mobile Responsive** - Works on tablets and phones

### 🎨 Modern Interface

- **React + Tailwind CSS** - Modern, responsive UI
- **Dark/Light Theme** - Customizable appearance
- **Intuitive Design** - Easy-to-use interface
- **Real-time Updates** - Live status and progress updates

### 🗄️ Database Support

- **SQLite** - For desktop/offline use
- **PostgreSQL** - For web/production deployment
- **Data Encryption** - Secure data storage
- **Backup & Restore** - Database backup functionality

## Installation

### Prerequisites

- Node.js 16+
- npm or yarn
- Git

### Development Setup

1. **Clone the repository**

```bash
git clone https://github.com/yourusername/syncmasterpro.git
cd syncmasterpro
```

1. **Install dependencies**

```bash
npm install
```

1. **Set up environment variables**

```bash
cp .env.example .env
# Edit .env with your configuration
```

1. **Start development servers**

```bash
# Start both React and Electron in development mode
npm start

# Or start individually:
npm run dev      # React development server
npm run server   # Node.js backend server
npm run electron-dev  # Electron app
```

### Production Build

1. **Build the application**

```bash
npm run build
```

1. **Package for distribution**

```bash
npm run dist
```

This will create installers for your platform in the `dist` folder.

## Usage

### Creating Sync Tasks

1. **Open SyncMasterPro**
2. **Click "New Sync Task"**
3. **Configure your sync:**
   - Choose source and destination folders
   - Select sync type (one-way or bidirectional)
   - Set up filters and exclusions
   - Configure schedule (optional)
4. **Start syncing**

### Sync Types

- **One-way Sync** - Source → Destination only
- **Bidirectional Sync** - Both directions with conflict resolution
- **Mirror Sync** - Exact copy with deletions
- **Backup Sync** - Incremental backup with versioning

### Scheduling

- **Manual** - Start sync manually
- **Real-time** - Continuous monitoring and sync
- **Interval** - Sync every X minutes/hours
- **Daily/Weekly** - Scheduled sync at specific times

## Configuration

### Environment Variables

See `.env.example` for all available configuration options.

### Database Configuration

#### SQLite (Default for Desktop)

```env
DB_TYPE=sqlite
```

#### PostgreSQL (For Web/Production)

```env
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_NAME=syncmasterpro
DB_USER=postgres
DB_PASSWORD=your_password
```

### Sync Configuration

```env
MAX_SYNC_TASKS=10
SYNC_TIMEOUT=300000
FILE_WATCH_DEBOUNCE=1000
ENABLE_REAL_TIME_SYNC=true
```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verify` - Verify token
- `PUT /api/auth/profile` - Update profile

### Sync Endpoints

- `GET /api/sync/tasks` - Get sync tasks
- `POST /api/sync/tasks` - Create sync task
- `PUT /api/sync/tasks/:id` - Update sync task
- `DELETE /api/sync/tasks/:id` - Delete sync task
- `POST /api/sync/tasks/:id/start` - Start sync
- `POST /api/sync/tasks/:id/stop` - Stop sync

### History Endpoints

- `GET /api/sync/history` - Get sync history
- `GET /api/sync/history/:id` - Get specific history entry

## Architecture

```text
SyncMasterPro/
├── electron/           # Electron main process
├── src/               # React frontend
│   ├── components/    # React components
│   ├── contexts/      # React contexts
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── server/            # Node.js backend
│   ├── routes/        # API routes
│   ├── middleware/    # Express middleware
│   ├── database/      # Database setup
│   └── services/      # Backend services
├── core/              # Core sync engine
└── database/          # Database schemas
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Sync not starting**
   - Check folder permissions
   - Verify paths exist
   - Check disk space

2. **Performance issues**
   - Reduce file watch debounce time
   - Exclude large files/folders
   - Check system resources

3. **Database errors**
   - Verify database configuration
   - Check connection settings
   - Ensure database exists

### Logs

Application logs are stored in:

- **Desktop**: `%APPDATA%/SyncMasterPro/logs/`
- **Development**: `./logs/`

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE)
file for details.

## Support

- **Documentation**: [docs.syncmasterpro.com](https://docs.syncmasterpro.com)
- **Issues**: [GitHub Issues](https://github.com/yourusername/syncmasterpro/issues)
- **Email**: <<EMAIL>>

## Roadmap

- [ ] Cloud storage integration (Google Drive, Dropbox, OneDrive)
- [ ] File versioning and rollback
- [ ] Advanced conflict resolution
- [ ] Mobile app for monitoring
- [ ] Plugin system for extensibility
- [ ] Enterprise features (LDAP, SSO)
- [ ] File encryption and compression
- [ ] Bandwidth throttling
- [ ] Network sync between computers

---

Made with ❤️ by the SyncMasterPro Team
