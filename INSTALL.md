# SyncMasterPro - Installation Guide

## System Requirements

### Minimum Requirements

- **Operating System**: Windows 10, macOS 10.14, or Ubuntu 18.04+
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space for application
- **Node.js**: Version 16.0 or higher
- **npm**: Version 7.0 or higher

### Recommended Requirements

- **RAM**: 8GB or more
- **Storage**: 1GB+ free space
- **SSD**: For better performance
- **Network**: Stable internet connection for web features

## Installation Methods

### Method 1: Quick Install (Recommended)

1. **Download and extract the project**

```bash
# If you have the source code
cd SyncMasterPro
```

1. **Install dependencies**

```bash
npm install
```

1. **Setup the application**

```bash
npm run setup
```

1. **Start the application**

```bash
npm start
```

### Method 2: Manual Installation

1. **Install Node.js**
   - Download from [nodejs.org](https://nodejs.org/)
   - Choose LTS version (recommended)
   - Verify installation: `node --version` and `npm --version`

2. **Clone or download the project**

```bash
git clone https://github.com/yourusername/syncmasterpro.git
cd syncmasterpro
```

3. **Install dependencies**

```bash
npm install
```

4. **Configure environment**

```bash
cp .env.example .env
# Edit .env file with your settings
```

5. **Create necessary directories**

```bash
mkdir data logs uploads temp
```

6. **Start development servers**

```bash
# Terminal 1: Start backend server
npm run server

# Terminal 2: Start React development server
npm run dev

# Terminal 3: Start Electron app
npm run electron-dev
```

### Method 3: Production Build

1. **Build the application**

```bash
npm run build
```

2. **Create distributable packages**

```bash
npm run dist
```

3. **Install the generated package**
   - Windows: Run the `.exe` installer from `dist/`
   - macOS: Open the `.dmg` file from `dist/`
   - Linux: Install the `.AppImage` or `.deb` file from `dist/`

## Configuration

### Environment Variables

Edit the `.env` file to configure your installation:

```env
# Application
NODE_ENV=development
PORT=5000
CLIENT_URL=http://localhost:3000

# Database (SQLite for desktop, PostgreSQL for web)
DB_TYPE=sqlite

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
REACT_APP_API_URL=http://localhost:5000/api
```

### Database Setup

#### SQLite (Default - No setup required)

The application will automatically create a SQLite database in the `data/` directory.

#### PostgreSQL (For web deployment)

1. Install PostgreSQL
2. Create a database:

```sql
CREATE DATABASE syncmasterpro;
CREATE USER syncuser WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE syncmasterpro TO syncuser;
```

3. Update `.env`:

```env
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_NAME=syncmasterpro
DB_USER=syncuser
DB_PASSWORD=your_password
```

## First Run

### Desktop Application

1. **Start the application**

```bash
npm start
```

2. **Create your account**
   - The app will open automatically
   - Click "Create Account" if you're a new user
   - Or use offline mode for local sync only

3. **Create your first sync task**
   - Click "New Sync Task"
   - Select source and destination folders
   - Configure sync settings
   - Start syncing

### Web Interface

1. **Access the web interface**
   - Open browser to `http://localhost:3000`
   - Create an account or sign in
   - Manage sync tasks remotely

## Troubleshooting

### Common Issues

#### 1. Port Already in Use

```bash
# Error: Port 3000 or 5000 already in use
# Solution: Change ports in .env file
PORT=5001
REACT_APP_API_URL=http://localhost:5001/api
```

#### 2. Permission Denied

```bash
# Error: EACCES permission denied
# Solution: Run with proper permissions or change directory
sudo npm install  # Linux/macOS
# Or choose a different directory with write permissions
```

#### 3. Node.js Version Issues

```bash
# Error: Unsupported Node.js version
# Solution: Update Node.js to version 16+
nvm install 16
nvm use 16
```

#### 4. Database Connection Failed

```bash
# Error: Database connection failed
# Solution: Check database configuration in .env
# For SQLite: Ensure data/ directory exists and is writable
# For PostgreSQL: Verify connection details and database exists
```

#### 5. Electron App Won't Start

```bash
# Error: Electron failed to start
# Solution: Rebuild electron
npm run postinstall
# Or reinstall electron
npm uninstall electron
npm install electron --save-dev
```

### Performance Optimization

#### 1. Large File Sync

- Increase memory limit: `NODE_OPTIONS="--max-old-space-size=4096"`
- Use file filters to exclude unnecessary files
- Enable compression for network transfers

#### 2. Many Files

- Adjust file watch debounce time in settings
- Use selective sync for large directories
- Consider batch processing for initial sync

#### 3. Network Issues

- Enable bandwidth limiting
- Use incremental sync mode
- Configure retry settings

### Logs and Debugging

#### Enable Debug Mode

```bash
# Set environment variable
DEBUG=syncmasterpro:*
npm start
```

#### Log Locations

- **Desktop**: `%APPDATA%/SyncMasterPro/logs/`
- **Development**: `./logs/`
- **Web**: Browser console and server logs

#### Common Log Messages

- `Sync started for task: [name]` - Normal operation
- `File watcher error` - Check file permissions
- `Database connection failed` - Check database config
- `Authentication failed` - Check credentials

## Uninstallation

### Desktop Application

1. **Windows**: Use "Add or Remove Programs"
2. **macOS**: Drag app to Trash
3. **Linux**: Use package manager or delete AppImage

### Development Environment

```bash
# Remove node modules
rm -rf node_modules

# Remove generated files
rm -rf build dist

# Remove data (optional)
rm -rf data logs uploads temp
```

### Clean Uninstall

```bash
# Remove all application data
# Windows: %APPDATA%/SyncMasterPro
# macOS: ~/Library/Application Support/SyncMasterPro
# Linux: ~/.config/SyncMasterPro
```

## Support

If you encounter issues not covered in this guide:

1. **Check the logs** for error messages
2. **Search existing issues** on GitHub
3. **Create a new issue** with:
   - Operating system and version
   - Node.js version
   - Error messages and logs
   - Steps to reproduce

## Next Steps

After successful installation:

1. **Read the User Guide** for detailed usage instructions
2. **Configure sync tasks** according to your needs
3. **Set up scheduling** for automatic synchronization
4. **Explore advanced features** like filters and conflict resolution

---

For more help, visit our [documentation](README.md) or contact support.
