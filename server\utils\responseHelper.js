/**
 * Response Helper Utility
 * 
 * Clean Code principles:
 * - Single Responsibility: Handles API response formatting
 * - DRY: Consistent response structure across all endpoints
 * - Error Handling: Standardized error responses
 * - Maintainability: Centralized response logic
 */

/**
 * Sends a successful response
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default: 200)
 */
const sendSuccess = (res, data = null, message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  res.status(statusCode).json(response);
};

/**
 * Sends a created response (201)
 * @param {Object} res - Express response object
 * @param {*} data - Created resource data
 * @param {string} message - Success message
 */
const sendCreated = (res, data, message = 'Resource created successfully') => {
  sendSuccess(res, data, message, 201);
};

/**
 * Sends an error response
 * @param {Object} res - Express response object
 * @param {string} error - Error type
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {*} details - Additional error details
 */
const sendError = (res, error, message, statusCode = 500, details = null) => {
  const response = {
    success: false,
    error,
    message,
    timestamp: new Date().toISOString()
  };

  if (details) {
    response.details = details;
  }

  res.status(statusCode).json(response);
};

/**
 * Sends a validation error response (400)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {*} details - Validation details
 */
const sendValidationError = (res, message = 'Validation failed', details = null) => {
  sendError(res, 'Validation Error', message, 400, details);
};

/**
 * Sends an unauthorized error response (401)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
const sendUnauthorized = (res, message = 'Unauthorized access') => {
  sendError(res, 'Unauthorized', message, 401);
};

/**
 * Sends a forbidden error response (403)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
const sendForbidden = (res, message = 'Access forbidden') => {
  sendError(res, 'Forbidden', message, 403);
};

/**
 * Sends a not found error response (404)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
const sendNotFound = (res, message = 'Resource not found') => {
  sendError(res, 'Not Found', message, 404);
};

/**
 * Sends a conflict error response (409)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
const sendConflict = (res, message = 'Resource conflict') => {
  sendError(res, 'Conflict', message, 409);
};

/**
 * Sends an internal server error response (500)
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {Error} error - Original error object (for logging)
 */
const sendInternalError = (res, message = 'Internal server error', error = null) => {
  if (error) {
    console.error('Internal Server Error:', error);
  }
  sendError(res, 'Internal Server Error', message, 500);
};

/**
 * Sends a paginated response
 * @param {Object} res - Express response object
 * @param {Array} items - Array of items
 * @param {Object} pagination - Pagination metadata
 * @param {string} message - Success message
 */
const sendPaginated = (res, items, pagination, message = 'Data retrieved successfully') => {
  const response = {
    success: true,
    message,
    data: items,
    pagination: {
      total: pagination.total,
      limit: pagination.limit,
      offset: pagination.offset,
      pages: pagination.pages,
      currentPage: pagination.currentPage,
      hasNext: pagination.currentPage < pagination.pages,
      hasPrev: pagination.currentPage > 1
    },
    timestamp: new Date().toISOString()
  };

  res.json(response);
};

/**
 * Handles service result and sends appropriate response
 * @param {Object} res - Express response object
 * @param {Object} result - Service result object
 * @param {number} successStatusCode - Status code for success (default: 200)
 */
const handleServiceResult = (res, result, successStatusCode = 200) => {
  if (result.success) {
    // Handle different types of successful results
    if (result.data) {
      sendSuccess(res, result.data, result.message, successStatusCode);
    } else if (result.user) {
      sendSuccess(res, { user: result.user, token: result.token }, result.message, successStatusCode);
    } else if (result.task) {
      sendSuccess(res, { task: result.task }, result.message, successStatusCode);
    } else if (result.tasks) {
      sendSuccess(res, { tasks: result.tasks, stats: result.stats }, result.message, successStatusCode);
    } else if (result.histories) {
      sendPaginated(res, result.histories, result.pagination, result.message);
    } else {
      sendSuccess(res, null, result.message, successStatusCode);
    }
  } else {
    // Determine appropriate error status code based on error message
    let statusCode = 400;
    
    if (result.error.includes('not found') || result.error.includes('access denied')) {
      statusCode = 404;
    } else if (result.error.includes('already exists')) {
      statusCode = 409;
    } else if (result.error.includes('unauthorized') || result.error.includes('invalid credentials')) {
      statusCode = 401;
    } else if (result.error.includes('permission') || result.error.includes('forbidden')) {
      statusCode = 403;
    }

    sendError(res, 'Operation Failed', result.error, statusCode);
  }
};

/**
 * Middleware to add response helpers to res object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const addResponseHelpers = (req, res, next) => {
  res.sendSuccess = (data, message, statusCode) => sendSuccess(res, data, message, statusCode);
  res.sendCreated = (data, message) => sendCreated(res, data, message);
  res.sendError = (error, message, statusCode, details) => sendError(res, error, message, statusCode, details);
  res.sendValidationError = (message, details) => sendValidationError(res, message, details);
  res.sendUnauthorized = (message) => sendUnauthorized(res, message);
  res.sendForbidden = (message) => sendForbidden(res, message);
  res.sendNotFound = (message) => sendNotFound(res, message);
  res.sendConflict = (message) => sendConflict(res, message);
  res.sendInternalError = (message, error) => sendInternalError(res, message, error);
  res.sendPaginated = (items, pagination, message) => sendPaginated(res, items, pagination, message);
  res.handleServiceResult = (result, successStatusCode) => handleServiceResult(res, result, successStatusCode);
  
  next();
};

/**
 * Transforms data to match frontend expectations
 * @param {Object} data - Data to transform
 * @param {Object} mapping - Field mapping object
 * @returns {Object} Transformed data
 */
const transformData = (data, mapping) => {
  if (!data || !mapping) return data;

  const transformed = {};
  
  for (const [frontendKey, backendKey] of Object.entries(mapping)) {
    if (data.hasOwnProperty(backendKey)) {
      transformed[frontendKey] = data[backendKey];
    }
  }

  // Copy unmapped fields
  for (const [key, value] of Object.entries(data)) {
    if (!Object.values(mapping).includes(key)) {
      transformed[key] = value;
    }
  }

  return transformed;
};

/**
 * Common field mappings for data transformation
 */
const fieldMappings = {
  syncTask: {
    sourcePath: 'source_path',
    destinationPath: 'destination_path',
    syncType: 'sync_type',
    lastSync: 'last_sync',
    filesCount: 'files_count',
    totalSize: 'total_size',
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
};

module.exports = {
  sendSuccess,
  sendCreated,
  sendError,
  sendValidationError,
  sendUnauthorized,
  sendForbidden,
  sendNotFound,
  sendConflict,
  sendInternalError,
  sendPaginated,
  handleServiceResult,
  addResponseHelpers,
  transformData,
  fieldMappings
};
